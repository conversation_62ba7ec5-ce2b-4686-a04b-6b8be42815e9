#!/bin/bash

# Bleen Agent Simple Setup Script
set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

print_status() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

print_status "🚀 Setting up Bleen Agent..."

# Check prerequisites
print_status "Checking prerequisites..."

if ! command -v docker &> /dev/null; then
    print_error "Docker is required but not installed"
    print_error "Please install Docker and try again"
    exit 1
fi

if ! command -v python3 &> /dev/null; then
    print_error "Python 3 is required but not installed"
    exit 1
fi

print_success "Prerequisites check passed"

# Create .env file
if [[ ! -f .env ]]; then
    print_status "Creating .env file..."
    cp .env.example .env
    print_warning "Please edit .env file with your OpenAI API key"
    print_status "Opening .env file for editing..."
    ${EDITOR:-nano} .env
else
    print_status ".env file already exists"
fi

# Create directories
print_status "Creating directories..."
mkdir -p data logs

# Install Python dependencies
print_status "Installing Python dependencies..."
pip install -r requirements.txt
pip install -e .

# Start Solr
print_status "Starting Solr..."
docker-compose up -d

# Wait for Solr to be ready
print_status "Waiting for Solr to be ready..."
for i in {1..30}; do
    if curl -s http://localhost:8983/solr/admin/ping > /dev/null 2>&1; then
        break
    fi
    if [[ $i -eq 30 ]]; then
        print_error "Solr failed to start after 30 attempts"
        print_error "Check logs with: docker-compose logs"
        exit 1
    fi
    sleep 2
done

print_success "Solr is running and healthy!"

# Verify Bleen Agent installation
print_status "Verifying Bleen Agent installation..."
if python -c "import bleen_agent" 2>/dev/null; then
    print_success "Bleen Agent installed successfully"
else
    print_error "Bleen Agent installation failed"
    exit 1
fi

print_success "🎉 Setup complete!"
print_status ""
print_status "Next steps:"
print_status "1. Make sure your OpenAI API key is set in .env"
print_status "2. Access Solr Admin UI: http://localhost:8983"
print_status "3. Start the agent: python -m bleen_agent.cli"
print_status ""
print_status "Useful commands:"
print_status "  make solr-logs    # View Solr logs"
print_status "  make solr-stop    # Stop Solr"
print_status "  make status       # Check status"
print_status "  make help         # See all commands"
