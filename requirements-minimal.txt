# Minimal requirements for core functionality
# Use this for faster installation during development

# Core LlamaIndex dependencies (essential only)
llama-index>=0.10.57
llama-index-agent-openai>=0.2.9
llama-index-llms-openai>=0.1.29
openai>=1.35.14

# Document processing and search
pysolr>=3.9.0
python-frontmatter>=1.1.0
tiktoken>=0.7.0

# CLI and utilities
click>=8.1.7
rich>=13.7.1
python-dotenv>=1.0.1

# Configuration
pydantic>=2.8.2
pydantic-settings>=2.4.0

# Basic utilities
requests>=2.32.3
