<?xml version="1.0" encoding="UTF-8" ?>
<!--
 Solr Schema for Bleen Agent Document Storage
 Optimized for both full-text search and structured attribute filtering
-->
<schema name="bleen-documents" version="1.6">

  <!-- Field Types -->
  <fieldType name="string" class="solr.StrField" sortMissingLast="true" />
  <fieldType name="boolean" class="solr.BoolField" sortMissingLast="true"/>
  <fieldType name="int" class="solr.IntPointField" docValues="true"/>
  <fieldType name="long" class="solr.LongPointField" docValues="true"/>
  <fieldType name="float" class="solr.FloatPointField" docValues="true"/>
  <fieldType name="double" class="solr.DoublePointField" docValues="true"/>
  <fieldType name="date" class="solr.DatePointField" docValues="true"/>
  
  <!-- Text field types for full-text search -->
  <fieldType name="text_general" class="solr.TextField" positionIncrementGap="100">
    <analyzer type="index">
      <tokenizer class="solr.StandardTokenizerFactory"/>
      <filter class="solr.StopFilterFactory" ignoreCase="true" words="stopwords.txt" />
      <filter class="solr.LowerCaseFilterFactory"/>
    </analyzer>
    <analyzer type="query">
      <tokenizer class="solr.StandardTokenizerFactory"/>
      <filter class="solr.StopFilterFactory" ignoreCase="true" words="stopwords.txt" />
      <filter class="solr.SynonymGraphFilterFactory" synonyms="synonyms.txt" ignoreCase="true" expand="true"/>
      <filter class="solr.LowerCaseFilterFactory"/>
    </analyzer>
  </fieldType>
  
  <!-- Enhanced text field for content with stemming -->
  <fieldType name="text_content" class="solr.TextField" positionIncrementGap="100">
    <analyzer type="index">
      <tokenizer class="solr.StandardTokenizerFactory"/>
      <filter class="solr.StopFilterFactory" ignoreCase="true" words="stopwords.txt" />
      <filter class="solr.LowerCaseFilterFactory"/>
      <filter class="solr.PorterStemFilterFactory"/>
    </analyzer>
    <analyzer type="query">
      <tokenizer class="solr.StandardTokenizerFactory"/>
      <filter class="solr.StopFilterFactory" ignoreCase="true" words="stopwords.txt" />
      <filter class="solr.SynonymGraphFilterFactory" synonyms="synonyms.txt" ignoreCase="true" expand="true"/>
      <filter class="solr.LowerCaseFilterFactory"/>
      <filter class="solr.PorterStemFilterFactory"/>
    </analyzer>
  </fieldType>
  
  <!-- Vector field for embeddings -->
  <fieldType name="knn_vector" class="solr.DenseVectorField" vectorDimension="1536" similarityFunction="cosine"/>

  <!-- Core Fields -->
  <field name="id" type="string" indexed="true" stored="true" required="true" multiValued="false" />
  <field name="_version_" type="long" indexed="false" stored="false"/>
  
  <!-- Document Metadata -->
  <field name="document_id" type="string" indexed="true" stored="true" required="true"/>
  <field name="chunk_id" type="string" indexed="true" stored="true" required="false"/>
  <field name="chunk_index" type="int" indexed="true" stored="true" required="false"/>
  <field name="is_chunk" type="boolean" indexed="true" stored="true" default="false"/>
  
  <!-- Document Attributes -->
  <field name="title" type="text_general" indexed="true" stored="true" required="false"/>
  <field name="author" type="string" indexed="true" stored="true" required="false" multiValued="true"/>
  <field name="category" type="string" indexed="true" stored="true" required="false" multiValued="true"/>
  <field name="tags" type="string" indexed="true" stored="true" required="false" multiValued="true"/>
  <field name="language" type="string" indexed="true" stored="true" required="false"/>
  <field name="document_type" type="string" indexed="true" stored="true" required="false"/>
  <field name="source_file" type="string" indexed="true" stored="true" required="false"/>
  <field name="file_path" type="string" indexed="true" stored="true" required="false"/>
  
  <!-- Temporal Fields -->
  <field name="created_date" type="date" indexed="true" stored="true" required="false"/>
  <field name="modified_date" type="date" indexed="true" stored="true" required="false"/>
  <field name="indexed_date" type="date" indexed="true" stored="true" default="NOW"/>
  
  <!-- Content Fields -->
  <field name="content" type="text_content" indexed="true" stored="true" required="true"/>
  <field name="summary" type="text_general" indexed="true" stored="true" required="false"/>
  <field name="keywords" type="string" indexed="true" stored="true" required="false" multiValued="true"/>
  
  <!-- Vector Embeddings -->
  <field name="content_vector" type="knn_vector" indexed="true" stored="true" required="false"/>
  
  <!-- Metrics and Quality -->
  <field name="content_length" type="int" indexed="true" stored="true" required="false"/>
  <field name="word_count" type="int" indexed="true" stored="true" required="false"/>
  <field name="readability_score" type="float" indexed="true" stored="true" required="false"/>
  
  <!-- Custom Fields for Frontmatter -->
  <dynamicField name="custom_*" type="text_general" indexed="true" stored="true" multiValued="true"/>
  <dynamicField name="meta_*" type="string" indexed="true" stored="true" multiValued="true"/>
  
  <!-- Copy Fields for Enhanced Search -->
  <copyField source="title" dest="content"/>
  <copyField source="summary" dest="content"/>
  <copyField source="keywords" dest="content"/>
  
  <!-- Unique Key -->
  <uniqueKey>id</uniqueKey>

</schema>
