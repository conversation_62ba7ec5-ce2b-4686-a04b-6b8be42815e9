#!/bin/bash

# Solr Collection Setup Script for Bleen Agent
# This script creates and configures the Solr collection for document storage

set -e

SOLR_URL=${SOLR_URL:-"http://localhost:8983/solr"}
COLLECTION_NAME=${COLLECTION_NAME:-"bleen_documents"}
CONFIG_NAME="${COLLECTION_NAME}_config"

echo "Setting up Solr collection: $COLLECTION_NAME"
echo "Solr URL: $SOLR_URL"

# Check if Solr is running
echo "Checking Solr status..."
if ! curl -s "$SOLR_URL/admin/info/system" > /dev/null; then
    echo "Error: Solr is not running at $SOLR_URL"
    echo "Please start Solr first: bin/solr start"
    exit 1
fi

# Create configuration set
echo "Creating configuration set..."
curl -X POST "$SOLR_URL/admin/configs" \
    -H 'Content-Type: application/json' \
    -d "{
        \"create\": {
            \"name\": \"$CONFIG_NAME\",
            \"baseConfigSet\": \"_default\"
        }
    }"

# Upload schema
echo "Uploading schema..."
curl -X POST "$SOLR_URL/admin/configs/$CONFIG_NAME/schema" \
    -H 'Content-Type: application/xml' \
    --data-binary @schema.xml

# Upload solrconfig
echo "Uploading solrconfig..."
curl -X POST "$SOLR_URL/admin/configs/$CONFIG_NAME/solrconfig.xml" \
    -H 'Content-Type: application/xml' \
    --data-binary @solrconfig.xml

# Create collection
echo "Creating collection..."
curl -X POST "$SOLR_URL/admin/collections" \
    -H 'Content-Type: application/json' \
    -d "{
        \"create\": {
            \"name\": \"$COLLECTION_NAME\",
            \"config\": \"$CONFIG_NAME\",
            \"numShards\": 1,
            \"replicationFactor\": 1
        }
    }"

# Verify collection creation
echo "Verifying collection..."
if curl -s "$SOLR_URL/admin/collections?action=LIST" | grep -q "$COLLECTION_NAME"; then
    echo "✅ Collection '$COLLECTION_NAME' created successfully!"
    echo "Collection URL: $SOLR_URL/$COLLECTION_NAME"
else
    echo "❌ Failed to create collection '$COLLECTION_NAME'"
    exit 1
fi

echo "Setup complete!"
echo ""
echo "Next steps:"
echo "1. Copy .env.example to .env and configure your settings"
echo "2. Install Python dependencies: pip install -r requirements.txt"
echo "3. Import documents: python -m bleen_agent.import_docs --help"
echo "4. Start the agent: python -m bleen_agent.cli"
