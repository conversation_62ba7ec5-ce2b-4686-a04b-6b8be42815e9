#!/bin/bash

# Bleen Agent - Solr Only Docker Startup Script
set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Function to print colored output
print_status() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Default values
WITH_ADMIN=false
DETACHED=false
RESET_DATA=false

# Function to show usage
show_usage() {
    echo "Usage: $0 [OPTIONS]"
    echo ""
    echo "Start only Apache Solr in Docker for Bleen Agent development"
    echo ""
    echo "Options:"
    echo "  -a, --with-admin         Start Solr admin proxy on port 8080"
    echo "  -d, --detached          Run in detached mode"
    echo "  -r, --reset-data        Reset Solr data (removes existing data)"
    echo "  -h, --help              Show this help message"
    echo ""
    echo "Examples:"
    echo "  $0                      # Start Solr only"
    echo "  $0 --with-admin         # Start Solr with admin proxy"
    echo "  $0 --detached           # Start in background"
    echo "  $0 --reset-data         # Start with fresh data"
}

# Parse command line arguments
while [[ $# -gt 0 ]]; do
    case $1 in
        -a|--with-admin)
            WITH_ADMIN=true
            shift
            ;;
        -d|--detached)
            DETACHED=true
            shift
            ;;
        -r|--reset-data)
            RESET_DATA=true
            shift
            ;;
        -h|--help)
            show_usage
            exit 0
            ;;
        *)
            print_error "Unknown option: $1"
            show_usage
            exit 1
            ;;
    esac
done

print_status "Starting Solr for Bleen Agent development..."

# Create data directory if it doesn't exist
mkdir -p ./data/solr

# Reset data if requested
if [[ "$RESET_DATA" == true ]]; then
    print_warning "Resetting Solr data..."
    if [[ -d "./data/solr" ]]; then
        rm -rf ./data/solr/*
        print_status "Solr data directory cleared"
    fi
fi

# Check if Solr is already running
if docker ps | grep -q "bleen-solr-standalone"; then
    print_warning "Solr container is already running"
    print_status "Stopping existing container..."
    docker-compose -f docker-compose.solr-only.yml down
fi

# Set detached flag
DETACH_FLAG=""
if [[ "$DETACHED" == true ]]; then
    DETACH_FLAG="-d"
    print_status "Running in detached mode"
fi

# Determine services to start
SERVICES="solr"
if [[ "$WITH_ADMIN" == true ]]; then
    SERVICES="$SERVICES solr-admin"
    print_status "Starting Solr with admin proxy"
fi

print_status "Starting Solr container..."
print_status "This may take a moment for initial setup..."

# Start Solr
docker-compose -f docker-compose.solr-only.yml up $DETACH_FLAG $SERVICES

if [[ "$DETACHED" == true ]]; then
    # Wait a moment for startup
    sleep 5
    
    # Check if Solr is healthy
    print_status "Checking Solr health..."
    for i in {1..30}; do
        if curl -s http://localhost:8983/solr/admin/ping > /dev/null 2>&1; then
            print_success "Solr is running and healthy!"
            break
        fi
        if [[ $i -eq 30 ]]; then
            print_error "Solr health check failed after 30 attempts"
            print_error "Check logs with: docker logs bleen-solr-standalone"
            exit 1
        fi
        sleep 2
    done
    
    print_success "Solr started successfully in detached mode"
    echo ""
    print_status "Access Points:"
    print_status "  Solr Admin UI: http://localhost:8983"
    if [[ "$WITH_ADMIN" == true ]]; then
        print_status "  Admin Proxy:   http://localhost:8080"
    fi
    echo ""
    print_status "Configuration for .env file:"
    print_status "  SOLR_URL=http://localhost:8983/solr"
    print_status "  SOLR_COLLECTION=bleen_documents"
    echo ""
    print_status "Useful commands:"
    print_status "  View logs:     docker logs -f bleen-solr-standalone"
    print_status "  Stop Solr:     ./scripts/stop-solr.sh"
    print_status "  Solr shell:    docker exec -it bleen-solr-standalone bash"
    echo ""
    print_status "Now you can run Bleen Agent locally with:"
    print_status "  python -m bleen_agent.cli"
else
    print_success "Solr started successfully"
    print_status "Press Ctrl+C to stop Solr"
fi
