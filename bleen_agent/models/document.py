"""
Document models for Bleen Agent.
Defines the structure for documents and chunks stored in Solr.
"""

from datetime import datetime
from typing import Dict, List, Optional, Any
from pydantic import BaseModel, Field
from enum import Enum


class DocumentType(str, Enum):
    """Supported document types."""
    MARKDOWN = "markdown"
    TEXT = "text"
    PDF = "pdf"
    HTML = "html"
    UNKNOWN = "unknown"


class Document(BaseModel):
    """
    Core document model representing a complete document.
    """
    
    # Core identifiers
    document_id: str = Field(..., description="Unique document identifier")
    title: Optional[str] = Field(None, description="Document title")
    
    # Content
    content: str = Field(..., description="Full document content")
    summary: Optional[str] = Field(None, description="Document summary")
    
    # Metadata
    author: Optional[List[str]] = Field(default_factory=list, description="Document authors")
    category: Optional[List[str]] = Field(default_factory=list, description="Document categories")
    tags: Optional[List[str]] = Field(default_factory=list, description="Document tags")
    keywords: Optional[List[str]] = Field(default_factory=list, description="Extracted keywords")
    language: Optional[str] = Field("en", description="Document language")
    document_type: DocumentType = Field(DocumentType.UNKNOWN, description="Document type")
    
    # File information
    source_file: Optional[str] = Field(None, description="Original filename")
    file_path: Optional[str] = Field(None, description="File path")
    
    # Temporal information
    created_date: Optional[datetime] = Field(None, description="Document creation date")
    modified_date: Optional[datetime] = Field(None, description="Document modification date")
    indexed_date: datetime = Field(default_factory=datetime.utcnow, description="Indexing timestamp")
    
    # Content metrics
    content_length: int = Field(0, description="Content length in characters")
    word_count: int = Field(0, description="Word count")
    readability_score: Optional[float] = Field(None, description="Readability score")
    
    # Custom fields from frontmatter
    custom_fields: Dict[str, Any] = Field(default_factory=dict, description="Custom metadata fields")
    
    # Vector embedding
    content_vector: Optional[List[float]] = Field(None, description="Content embedding vector")
    
    def __post_init__(self):
        """Calculate derived fields after initialization."""
        if not self.content_length:
            self.content_length = len(self.content)
        if not self.word_count:
            self.word_count = len(self.content.split())


class DocumentChunk(BaseModel):
    """
    Document chunk model for storing document segments.
    """
    
    # Core identifiers
    chunk_id: str = Field(..., description="Unique chunk identifier")
    document_id: str = Field(..., description="Parent document identifier")
    chunk_index: int = Field(..., description="Chunk position in document")
    
    # Content
    content: str = Field(..., description="Chunk content")
    
    # Inherited metadata from parent document
    title: Optional[str] = Field(None, description="Parent document title")
    author: Optional[List[str]] = Field(default_factory=list, description="Document authors")
    category: Optional[List[str]] = Field(default_factory=list, description="Document categories")
    tags: Optional[List[str]] = Field(default_factory=list, description="Document tags")
    language: Optional[str] = Field("en", description="Document language")
    document_type: DocumentType = Field(DocumentType.UNKNOWN, description="Document type")
    source_file: Optional[str] = Field(None, description="Original filename")
    file_path: Optional[str] = Field(None, description="File path")
    
    # Chunk-specific metadata
    start_char: int = Field(0, description="Start character position in original document")
    end_char: int = Field(0, description="End character position in original document")
    content_length: int = Field(0, description="Chunk content length")
    word_count: int = Field(0, description="Chunk word count")
    
    # Temporal information
    indexed_date: datetime = Field(default_factory=datetime.utcnow, description="Indexing timestamp")
    
    # Vector embedding
    content_vector: Optional[List[float]] = Field(None, description="Chunk embedding vector")
    
    # Chunk context
    previous_chunk_id: Optional[str] = Field(None, description="Previous chunk ID for context")
    next_chunk_id: Optional[str] = Field(None, description="Next chunk ID for context")
    
    def __post_init__(self):
        """Calculate derived fields after initialization."""
        if not self.content_length:
            self.content_length = len(self.content)
        if not self.word_count:
            self.word_count = len(self.content.split())


class SearchResult(BaseModel):
    """
    Search result model containing document or chunk with relevance information.
    """
    
    # Core identifiers
    id: str = Field(..., description="Document or chunk ID")
    document_id: str = Field(..., description="Parent document ID")
    is_chunk: bool = Field(False, description="Whether this is a chunk or full document")
    
    # Content
    content: str = Field(..., description="Retrieved content")
    title: Optional[str] = Field(None, description="Document title")
    
    # Relevance information
    score: float = Field(0.0, description="Search relevance score")
    search_type: str = Field("", description="Type of search that returned this result")
    
    # Metadata
    metadata: Dict[str, Any] = Field(default_factory=dict, description="Additional metadata")
    
    # Highlighting
    highlights: Dict[str, List[str]] = Field(default_factory=dict, description="Search term highlights")


class QueryContext(BaseModel):
    """
    Context information for query processing.
    """
    
    # Query information
    original_query: str = Field(..., description="Original user query")
    processed_query: str = Field("", description="Processed/cleaned query")
    query_type: str = Field("", description="Determined query type")
    
    # Search strategy
    search_strategy: str = Field("", description="Selected search strategy")
    filters: Dict[str, Any] = Field(default_factory=dict, description="Applied filters")
    
    # Results
    num_results_requested: int = Field(10, description="Number of results requested")
    num_results_found: int = Field(0, description="Number of results found")
    
    # Performance
    search_time_ms: float = Field(0.0, description="Search execution time in milliseconds")
    
    # Context
    conversation_history: List[str] = Field(default_factory=list, description="Previous queries in conversation")
