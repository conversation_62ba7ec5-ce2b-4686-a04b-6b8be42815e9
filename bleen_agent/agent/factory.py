"""
Agent factory for creating and configuring Bleen Agent instances.

This module provides factory functions for creating agents with different
tool configurations based on use cases and requirements.
"""

from typing import List, Optional, Dict, Any
from llama_index.core.tools import BaseTool

from .core import Bleen<PERSON>gent
from ..tools import (
    get_all_tools, 
    get_search_tools, 
    get_management_tools, 
    get_analysis_tools
)
from ..core.config import settings
from ..utils.logging import get_logger

logger = get_logger(__name__)


class AgentFactory:
    """
    Factory for creating configured Bleen Agent instances.
    
    Provides different agent configurations:
    - Full agent with all tools
    - Search-only agent for query answering
    - Management agent for document operations
    - Analysis agent for insights and statistics
    """
    
    @staticmethod
    def create_full_agent() -> BleenAgent:
        """
        Create a full-featured agent with all available tools.
        
        This is the default configuration for most use cases.
        
        Returns:
            BleenAgent instance with all tools
        """
        logger.info("Creating full-featured Bleen Agent")
        
        tools = get_all_tools()
        agent = BleenAgent(tools=tools)
        
        logger.info("Full agent created", 
                   total_tools=len(tools),
                   tool_names=[tool.metadata.name for tool in tools])
        
        return agent
    
    @staticmethod
    def create_search_agent() -> Bleen<PERSON>gent:
        """
        Create an agent focused on search and retrieval operations.
        
        Includes:
        - Document search
        - Filtered search
        - Similar documents
        - Query analysis
        - Collection stats
        
        Returns:
            BleenAgent instance with search tools
        """
        logger.info("Creating search-focused Bleen Agent")
        
        tools = get_search_tools() + get_analysis_tools()
        agent = BleenAgent(tools=tools)
        
        logger.info("Search agent created", 
                   total_tools=len(tools),
                   tool_names=[tool.metadata.name for tool in tools])
        
        return agent
    
    @staticmethod
    def create_management_agent() -> BleenAgent:
        """
        Create an agent focused on document management operations.
        
        Includes:
        - Document import
        - Document deletion
        - Document details
        - Collection stats
        
        Returns:
            BleenAgent instance with management tools
        """
        logger.info("Creating management-focused Bleen Agent")
        
        tools = get_management_tools() + get_analysis_tools()
        agent = BleenAgent(tools=tools)
        
        logger.info("Management agent created", 
                   total_tools=len(tools),
                   tool_names=[tool.metadata.name for tool in tools])
        
        return agent
    
    @staticmethod
    def create_custom_agent(tool_names: List[str]) -> BleenAgent:
        """
        Create an agent with a custom selection of tools.
        
        Args:
            tool_names: List of tool names to include
            
        Returns:
            BleenAgent instance with selected tools
        """
        logger.info("Creating custom Bleen Agent", tool_names=tool_names)
        
        # Get all available tools
        all_tools = get_all_tools()
        tool_map = {tool.metadata.name: tool for tool in all_tools}
        
        # Select requested tools
        selected_tools = []
        missing_tools = []
        
        for tool_name in tool_names:
            if tool_name in tool_map:
                selected_tools.append(tool_map[tool_name])
            else:
                missing_tools.append(tool_name)
        
        if missing_tools:
            logger.warning("Some requested tools not found", missing_tools=missing_tools)
        
        if not selected_tools:
            logger.warning("No valid tools selected, creating agent with search tools")
            selected_tools = get_search_tools()
        
        agent = BleenAgent(tools=selected_tools)
        
        logger.info("Custom agent created", 
                   total_tools=len(selected_tools),
                   tool_names=[tool.metadata.name for tool in selected_tools],
                   missing_tools=missing_tools)
        
        return agent
    
    @staticmethod
    def create_agent_from_config(config: Dict[str, Any]) -> BleenAgent:
        """
        Create an agent from a configuration dictionary.
        
        Args:
            config: Configuration dictionary with agent settings
            
        Returns:
            BleenAgent instance configured according to config
        """
        logger.info("Creating agent from configuration", config=config)
        
        agent_type = config.get("type", "full")
        
        if agent_type == "full":
            agent = AgentFactory.create_full_agent()
        elif agent_type == "search":
            agent = AgentFactory.create_search_agent()
        elif agent_type == "management":
            agent = AgentFactory.create_management_agent()
        elif agent_type == "custom":
            tool_names = config.get("tools", [])
            agent = AgentFactory.create_custom_agent(tool_names)
        else:
            logger.warning("Unknown agent type, creating full agent", agent_type=agent_type)
            agent = AgentFactory.create_full_agent()
        
        # Apply additional configuration if provided
        if "memory_max_tokens" in config:
            agent.memory.max_tokens = config["memory_max_tokens"]
        
        logger.info("Agent created from configuration", 
                   agent_type=agent_type,
                   session_id=agent.session_id)
        
        return agent
    
    @staticmethod
    def get_available_tools() -> Dict[str, str]:
        """
        Get information about all available tools.
        
        Returns:
            Dictionary mapping tool names to descriptions
        """
        tools = get_all_tools()
        return {
            tool.metadata.name: tool.metadata.description 
            for tool in tools
        }
    
    @staticmethod
    def validate_solr_connection() -> bool:
        """
        Validate that Solr is accessible before creating agents.
        
        Returns:
            True if Solr is accessible, False otherwise
        """
        try:
            from ..tools.search import SolrConnection
            solr_conn = SolrConnection()
            return solr_conn.ping()
        except Exception as e:
            logger.error("Solr connection validation failed", error=str(e))
            return False


# Convenience functions for common agent types
def create_agent(agent_type: str = "full", **kwargs) -> BleenAgent:
    """
    Convenience function to create an agent of a specific type.
    
    Args:
        agent_type: Type of agent to create ("full", "search", "management", "custom")
        **kwargs: Additional arguments for custom agent creation
        
    Returns:
        BleenAgent instance
    """
    if agent_type == "full":
        return AgentFactory.create_full_agent()
    elif agent_type == "search":
        return AgentFactory.create_search_agent()
    elif agent_type == "management":
        return AgentFactory.create_management_agent()
    elif agent_type == "custom":
        tool_names = kwargs.get("tools", [])
        return AgentFactory.create_custom_agent(tool_names)
    else:
        raise ValueError(f"Unknown agent type: {agent_type}")


def create_agent_with_health_check() -> Optional[BleenAgent]:
    """
    Create a full agent after validating system health.
    
    Returns:
        BleenAgent instance if healthy, None if validation fails
    """
    logger.info("Creating agent with health check")
    
    # Validate Solr connection
    if not AgentFactory.validate_solr_connection():
        logger.error("Health check failed: Cannot connect to Solr")
        return None
    
    # Create agent
    try:
        agent = AgentFactory.create_full_agent()
        logger.info("Agent created successfully with health check passed")
        return agent
    except Exception as e:
        logger.error("Agent creation failed", error=str(e))
        return None


# Agent configuration presets
AGENT_PRESETS = {
    "researcher": {
        "type": "custom",
        "tools": [
            "document_search",
            "filtered_search", 
            "find_similar_documents",
            "analyze_query",
            "get_collection_stats"
        ],
        "description": "Agent optimized for research and information discovery"
    },
    
    "librarian": {
        "type": "custom", 
        "tools": [
            "import_documents",
            "delete_document",
            "get_document_details",
            "get_collection_stats",
            "document_search"
        ],
        "description": "Agent optimized for document management and organization"
    },
    
    "analyst": {
        "type": "custom",
        "tools": [
            "analyze_query",
            "get_collection_stats",
            "document_search",
            "filtered_search"
        ],
        "description": "Agent optimized for analysis and insights"
    }
}


def create_preset_agent(preset_name: str) -> BleenAgent:
    """
    Create an agent using a predefined preset configuration.
    
    Args:
        preset_name: Name of the preset ("researcher", "librarian", "analyst")
        
    Returns:
        BleenAgent instance configured with preset
    """
    if preset_name not in AGENT_PRESETS:
        available_presets = list(AGENT_PRESETS.keys())
        raise ValueError(f"Unknown preset: {preset_name}. Available: {available_presets}")
    
    config = AGENT_PRESETS[preset_name]
    logger.info("Creating preset agent", 
               preset_name=preset_name,
               description=config["description"])
    
    return AgentFactory.create_agent_from_config(config)
