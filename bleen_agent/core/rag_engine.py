"""
RAG (Retrieval-Augmented Generation) engine using LlamaIndex and Solr.
"""

from typing import List, Optional, Dict, Any
from llama_index.core import VectorStoreIndex, ServiceContext, get_response_synthesizer
from llama_index.core.retrievers import VectorIndexRetriever
from llama_index.core.query_engine import Retriever<PERSON><PERSON>y<PERSON>ngine
from llama_index.core.postprocessor import SimilarityPostprocessor
from llama_index.core.schema import NodeWithScore, TextNode
from llama_index.llms.openai import OpenAI
from llama_index.embeddings.openai import OpenAIEmbedding

from ..core.config import settings
from ..solr.client import SolrClient
from ..models.document import SearchResult, QueryContext
from ..utils.logging import AgentLogger

logger = AgentLogger(__name__)


class SolrVectorStore:
    """
    Custom vector store implementation that uses Solr as the backend.
    """
    
    def __init__(self, solr_client: SolrClient):
        self.solr_client = solr_client
    
    def add(self, nodes: List[TextNode]) -> List[str]:
        """Add nodes to the vector store."""
        node_ids = []
        
        for node in nodes:
            # Convert TextNode to our document format and index
            # This would typically be handled by the document importer
            node_ids.append(node.node_id)
        
        return node_ids
    
    def delete(self, ref_doc_id: str, **delete_kwargs: Any) -> None:
        """Delete nodes by reference document ID."""
        self.solr_client.delete_document(ref_doc_id)
    
    def query(self, query_embedding: List[float], similarity_top_k: int = 10) -> List[NodeWithScore]:
        """Query the vector store using embedding similarity."""
        # Convert embedding to query format for Solr
        # This is a simplified implementation - in practice, you'd use Solr's vector search
        results = self.solr_client.search_vector(
            query="", # We'd pass the embedding directly in a real implementation
            max_results=similarity_top_k
        )
        
        # Convert SearchResults to NodeWithScore
        nodes_with_scores = []
        for result in results:
            node = TextNode(
                text=result.content,
                node_id=result.id,
                metadata=result.metadata
            )
            node_with_score = NodeWithScore(node=node, score=result.score)
            nodes_with_scores.append(node_with_score)
        
        return nodes_with_scores


class BleenRAGEngine:
    """
    Main RAG engine that combines Solr search with LlamaIndex for answer generation.
    """
    
    def __init__(self, solr_client: Optional[SolrClient] = None):
        self.solr_client = solr_client or SolrClient()
        
        # Initialize LLM
        self.llm = OpenAI(
            model=settings.llm_model,
            temperature=settings.llm_temperature,
            max_tokens=settings.llm_max_tokens,
            api_key=settings.openai_api_key
        )
        
        # Initialize embedding model
        self.embed_model = OpenAIEmbedding(
            model=settings.embedding_model,
            api_key=settings.openai_api_key
        )
        
        # Initialize service context
        self.service_context = ServiceContext.from_defaults(
            llm=self.llm,
            embed_model=self.embed_model
        )
        
        # Initialize vector store and index
        self.vector_store = SolrVectorStore(self.solr_client)
        self.vector_index = VectorStoreIndex.from_vector_store(
            vector_store=self.vector_store,
            service_context=self.service_context
        )
        
        logger.info("RAG engine initialized")
    
    def query(
        self, 
        query: str, 
        search_strategy: str = "hybrid",
        max_results: int = None,
        filters: Dict[str, Any] = None
    ) -> Dict[str, Any]:
        """
        Process a query and generate an answer using RAG.
        
        Args:
            query: User query
            search_strategy: Search strategy ("fulltext", "vector", "hybrid")
            max_results: Maximum number of results to retrieve
            filters: Additional filters for search
            
        Returns:
            Dictionary containing answer and metadata
        """
        max_results = max_results or settings.max_search_results
        
        logger.log_step("Starting RAG Query", {
            "query": query,
            "search_strategy": search_strategy,
            "max_results": max_results
        })
        
        # Create query context
        query_context = QueryContext(
            original_query=query,
            processed_query=query,
            search_strategy=search_strategy,
            filters=filters or {},
            num_results_requested=max_results
        )
        
        try:
            # Retrieve relevant documents
            search_results = self._retrieve_documents(query, search_strategy, max_results, filters)
            
            query_context.num_results_found = len(search_results)
            
            logger.log_retrieval_results(
                num_results=len(search_results),
                sources=[r.metadata.get('source_file', r.id) for r in search_results]
            )
            
            if not search_results:
                return {
                    "answer": "I couldn't find any relevant information to answer your question.",
                    "sources": [],
                    "query_context": query_context,
                    "confidence": 0.0
                }
            
            # Generate answer using retrieved context
            answer_data = self._generate_answer(query, search_results)
            
            logger.log_answer_generation(
                context_length=sum(len(r.content) for r in search_results),
                answer_preview=answer_data["answer"]
            )
            
            return {
                "answer": answer_data["answer"],
                "sources": self._format_sources(search_results),
                "query_context": query_context,
                "confidence": answer_data.get("confidence", 0.5),
                "reasoning": answer_data.get("reasoning", "")
            }
            
        except Exception as e:
            logger.error("RAG query failed", query=query, error=str(e))
            return {
                "answer": f"I encountered an error while processing your question: {str(e)}",
                "sources": [],
                "query_context": query_context,
                "confidence": 0.0
            }
    
    def _retrieve_documents(
        self, 
        query: str, 
        strategy: str, 
        max_results: int,
        filters: Dict[str, Any] = None
    ) -> List[SearchResult]:
        """Retrieve relevant documents using the specified strategy."""
        
        if strategy == "fulltext":
            return self.solr_client.search_fulltext(query, filters, max_results)
        elif strategy == "vector":
            return self.solr_client.search_vector(query, filters, max_results)
        elif strategy == "hybrid":
            return self.solr_client.search_hybrid(query, filters, max_results)
        else:
            logger.warning("Unknown search strategy, using hybrid", strategy=strategy)
            return self.solr_client.search_hybrid(query, filters, max_results)
    
    def _generate_answer(self, query: str, search_results: List[SearchResult]) -> Dict[str, Any]:
        """Generate an answer using the LLM and retrieved context."""
        
        # Prepare context from search results
        context_parts = []
        for i, result in enumerate(search_results):
            source_info = result.metadata.get('source_file', result.id)
            context_parts.append(f"[Source {i+1}: {source_info}]\n{result.content}")
        
        context = "\n\n".join(context_parts)
        
        # Create prompt
        prompt = self._build_answer_prompt(query, context)
        
        # Generate response
        try:
            response = self.llm.complete(prompt)
            answer = response.text.strip()
            
            # Extract confidence if the model provides it
            confidence = self._extract_confidence(answer)
            
            return {
                "answer": answer,
                "confidence": confidence,
                "reasoning": "Generated using retrieved context and LLM"
            }
            
        except Exception as e:
            logger.error("Answer generation failed", error=str(e))
            return {
                "answer": "I encountered an error while generating the answer.",
                "confidence": 0.0,
                "reasoning": f"Error: {str(e)}"
            }
    
    def _build_answer_prompt(self, query: str, context: str) -> str:
        """Build the prompt for answer generation."""
        return f"""You are a helpful AI assistant that answers questions based on provided context.

Context:
{context}

Question: {query}

Instructions:
1. Answer the question based solely on the provided context
2. If the context doesn't contain enough information to answer the question, say so
3. Be concise but comprehensive
4. Cite specific sources when possible
5. If you're uncertain, express that uncertainty

Answer:"""
    
    def _extract_confidence(self, answer: str) -> float:
        """Extract confidence score from the answer (simplified implementation)."""
        # This is a simplified implementation
        # In practice, you might use the model's logits or train a separate confidence model
        
        uncertainty_phrases = [
            "i'm not sure", "i don't know", "unclear", "uncertain", 
            "might be", "possibly", "perhaps", "it seems"
        ]
        
        answer_lower = answer.lower()
        
        if any(phrase in answer_lower for phrase in uncertainty_phrases):
            return 0.3
        elif "based on the context" in answer_lower or "according to" in answer_lower:
            return 0.8
        else:
            return 0.6
    
    def _format_sources(self, search_results: List[SearchResult]) -> List[Dict[str, Any]]:
        """Format search results as source references."""
        sources = []
        
        for i, result in enumerate(search_results):
            source = {
                "id": result.id,
                "title": result.title or "Untitled",
                "source_file": result.metadata.get('source_file', 'Unknown'),
                "document_type": result.metadata.get('document_type', 'unknown'),
                "relevance_score": result.score,
                "is_chunk": result.is_chunk,
                "preview": result.content[:200] + "..." if len(result.content) > 200 else result.content
            }
            sources.append(source)
        
        return sources
    
    def get_similar_documents(self, document_id: str, max_results: int = 5) -> List[SearchResult]:
        """Find documents similar to a given document."""
        logger.log_step("Finding Similar Documents", {
            "document_id": document_id,
            "max_results": max_results
        })
        
        # Get the document content
        doc = self.solr_client.get_document(document_id)
        if not doc:
            logger.warning("Document not found for similarity search", document_id=document_id)
            return []
        
        # Use the document content as query for similarity search
        content = doc.get('content', '')
        if not content:
            return []
        
        # Perform vector search using document content
        similar_docs = self.solr_client.search_vector(
            query=content,
            filters={"document_id": f"-{document_id}"},  # Exclude the original document
            max_results=max_results
        )
        
        logger.log_retrieval_results(
            num_results=len(similar_docs),
            sources=[d.metadata.get('source_file', d.id) for d in similar_docs]
        )
        
        return similar_docs
