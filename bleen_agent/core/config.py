"""
Configuration management for Bleen Agent using Pydantic Settings.
"""

from typing import Optional
from pydantic import Field
from pydantic_settings import BaseSettings


class Settings(BaseSettings):
    """Application settings loaded from environment variables."""
    
    # OpenAI Configuration
    openai_api_key: str = Field(..., description="OpenAI API key")
    
    # Solr Configuration
    solr_url: str = Field(default="http://localhost:8983/solr", description="Solr server URL")
    solr_collection: str = Field(default="bleen_documents", description="Solr collection name")
    
    # Agent Configuration
    log_level: str = Field(default="INFO", description="Logging level")
    max_chunk_size: int = Field(default=1000, description="Maximum chunk size for document splitting")
    chunk_overlap: int = Field(default=200, description="Overlap between chunks")
    max_search_results: int = Field(default=10, description="Maximum number of search results to retrieve")
    
    # LLM Configuration
    llm_model: str = Field(default="gpt-3.5-turbo", description="OpenAI model to use")
    llm_temperature: float = Field(default=0.1, description="LLM temperature for response generation")
    llm_max_tokens: int = Field(default=2000, description="Maximum tokens for LLM response")
    
    # Embedding Configuration
    embedding_model: str = Field(default="text-embedding-ada-002", description="OpenAI embedding model")
    embedding_dimension: int = Field(default=1536, description="Embedding vector dimension")
    
    class Config:
        env_file = ".env"
        env_file_encoding = "utf-8"
        case_sensitive = False


# Global settings instance
settings = Settings()
