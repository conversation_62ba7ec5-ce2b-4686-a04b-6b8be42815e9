"""
Query analysis and search strategy determination for Bleen Agent.
"""

import re
from typing import Dict, List, Optional, Any, Tuple
from enum import Enum
from dataclasses import dataclass

from ..utils.logging import AgentLogger

logger = AgentLogger(__name__)


class QueryType(str, Enum):
    """Types of queries the system can handle."""
    FACTUAL = "factual"           # Direct factual questions
    CONCEPTUAL = "conceptual"     # Conceptual or explanatory questions
    PROCEDURAL = "procedural"     # How-to or step-by-step questions
    COMPARATIVE = "comparative"   # Comparison questions
    DEFINITIONAL = "definitional" # Definition questions
    TEMPORAL = "temporal"         # Time-based questions
    CAUSAL = "causal"            # Cause-and-effect questions
    EXPLORATORY = "exploratory"   # Open-ended exploration
    UNKNOWN = "unknown"


class SearchStrategy(str, Enum):
    """Available search strategies."""
    FULLTEXT = "fulltext"         # Full-text search only
    VECTOR = "vector"             # Vector similarity search only
    HYBRID = "hybrid"             # Combined full-text and vector search
    ATTRIBUTE = "attribute"       # Attribute-based filtering
    HYBRID_ATTRIBUTE = "hybrid_attribute"  # Hybrid search with attribute filters


@dataclass
class QueryAnalysis:
    """Results of query analysis."""
    original_query: str
    processed_query: str
    query_type: QueryType
    search_strategy: SearchStrategy
    key_terms: List[str]
    filters: Dict[str, Any]
    confidence: float
    reasoning: str


class QueryAnalyzer:
    """
    Intelligent query analyzer that determines the best search strategy.
    """
    
    def __init__(self):
        self.question_patterns = self._initialize_patterns()
        self.filter_extractors = self._initialize_filter_extractors()
    
    def analyze_query(self, query: str) -> QueryAnalysis:
        """
        Analyze a user query and determine the optimal search strategy.
        
        Args:
            query: User's natural language query
            
        Returns:
            QueryAnalysis with strategy and parameters
        """
        logger.log_step("Analyzing Query", {"query": query})
        
        # Clean and preprocess the query
        processed_query = self._preprocess_query(query)
        
        # Determine query type
        query_type = self._classify_query_type(processed_query)
        
        # Extract key terms
        key_terms = self._extract_key_terms(processed_query)
        
        # Extract filters from query
        filters = self._extract_filters(processed_query)
        
        # Determine search strategy
        search_strategy, confidence, reasoning = self._determine_search_strategy(
            processed_query, query_type, key_terms, filters
        )
        
        analysis = QueryAnalysis(
            original_query=query,
            processed_query=processed_query,
            query_type=query_type,
            search_strategy=search_strategy,
            key_terms=key_terms,
            filters=filters,
            confidence=confidence,
            reasoning=reasoning
        )
        
        logger.log_step("Query Analysis Complete", {
            "query_type": query_type.value,
            "search_strategy": search_strategy.value,
            "key_terms": key_terms,
            "filters": filters,
            "confidence": confidence
        })
        
        return analysis
    
    def _preprocess_query(self, query: str) -> str:
        """Clean and preprocess the query."""
        # Remove extra whitespace
        query = re.sub(r'\s+', ' ', query.strip())
        
        # Convert to lowercase for analysis (but keep original case for display)
        return query
    
    def _classify_query_type(self, query: str) -> QueryType:
        """Classify the type of query based on patterns."""
        query_lower = query.lower()
        
        # Check each pattern type
        for query_type, patterns in self.question_patterns.items():
            for pattern in patterns:
                if re.search(pattern, query_lower):
                    return QueryType(query_type)
        
        return QueryType.UNKNOWN
    
    def _extract_key_terms(self, query: str) -> List[str]:
        """Extract key terms from the query."""
        # Remove common stop words and question words
        stop_words = {
            'what', 'how', 'when', 'where', 'why', 'who', 'which', 'whose',
            'is', 'are', 'was', 'were', 'be', 'been', 'being',
            'the', 'a', 'an', 'and', 'or', 'but', 'in', 'on', 'at', 'to', 'for',
            'of', 'with', 'by', 'about', 'can', 'could', 'should', 'would',
            'do', 'does', 'did', 'have', 'has', 'had'
        }
        
        # Extract words (simple tokenization)
        words = re.findall(r'\b\w+\b', query.lower())
        
        # Filter out stop words and short words
        key_terms = [word for word in words if word not in stop_words and len(word) > 2]
        
        # Extract quoted phrases
        quoted_phrases = re.findall(r'"([^"]+)"', query)
        key_terms.extend(quoted_phrases)
        
        return key_terms
    
    def _extract_filters(self, query: str) -> Dict[str, Any]:
        """Extract attribute-based filters from the query."""
        filters = {}
        
        for filter_type, extractors in self.filter_extractors.items():
            for extractor in extractors:
                matches = extractor(query)
                if matches:
                    if filter_type in filters:
                        if isinstance(filters[filter_type], list):
                            filters[filter_type].extend(matches)
                        else:
                            filters[filter_type] = [filters[filter_type]] + matches
                    else:
                        filters[filter_type] = matches if len(matches) > 1 else matches[0]
        
        return filters
    
    def _determine_search_strategy(
        self, 
        query: str, 
        query_type: QueryType, 
        key_terms: List[str],
        filters: Dict[str, Any]
    ) -> Tuple[SearchStrategy, float, str]:
        """
        Determine the best search strategy based on query analysis.
        
        Returns:
            Tuple of (strategy, confidence, reasoning)
        """
        
        # If we have specific filters, use attribute-based search
        if filters:
            if len(key_terms) > 2:
                return (
                    SearchStrategy.HYBRID_ATTRIBUTE,
                    0.9,
                    "Query contains both content terms and specific filters"
                )
            else:
                return (
                    SearchStrategy.ATTRIBUTE,
                    0.8,
                    "Query is primarily filter-based"
                )
        
        # Strategy based on query type
        if query_type == QueryType.DEFINITIONAL:
            return (
                SearchStrategy.FULLTEXT,
                0.8,
                "Definitional queries work best with full-text search"
            )
        
        elif query_type == QueryType.FACTUAL:
            return (
                SearchStrategy.HYBRID,
                0.9,
                "Factual queries benefit from both semantic and keyword matching"
            )
        
        elif query_type == QueryType.CONCEPTUAL:
            return (
                SearchStrategy.VECTOR,
                0.8,
                "Conceptual queries work best with semantic similarity"
            )
        
        elif query_type == QueryType.PROCEDURAL:
            return (
                SearchStrategy.HYBRID,
                0.8,
                "Procedural queries benefit from both approaches"
            )
        
        elif query_type == QueryType.COMPARATIVE:
            return (
                SearchStrategy.VECTOR,
                0.7,
                "Comparative queries work well with semantic search"
            )
        
        # Strategy based on query characteristics
        query_lower = query.lower()
        
        # Long, complex queries often benefit from vector search
        if len(query.split()) > 10:
            return (
                SearchStrategy.VECTOR,
                0.7,
                "Long queries work well with semantic similarity"
            )
        
        # Queries with specific technical terms benefit from full-text
        if any(term.isupper() or '_' in term or '-' in term for term in key_terms):
            return (
                SearchStrategy.FULLTEXT,
                0.7,
                "Query contains technical terms that benefit from exact matching"
            )
        
        # Default to hybrid search
        return (
            SearchStrategy.HYBRID,
            0.6,
            "Using hybrid search as default strategy"
        )
    
    def _initialize_patterns(self) -> Dict[str, List[str]]:
        """Initialize regex patterns for query type classification."""
        return {
            "definitional": [
                r'\bwhat is\b',
                r'\bwhat are\b',
                r'\bdefine\b',
                r'\bdefinition of\b',
                r'\bmeaning of\b',
                r'\bexplain what\b'
            ],
            "factual": [
                r'\bwhen did\b',
                r'\bwhere is\b',
                r'\bwho is\b',
                r'\bhow many\b',
                r'\bhow much\b',
                r'\bwhat year\b',
                r'\bwhich\b.*\bis\b'
            ],
            "procedural": [
                r'\bhow to\b',
                r'\bhow do i\b',
                r'\bhow can i\b',
                r'\bsteps to\b',
                r'\bprocess of\b',
                r'\bway to\b'
            ],
            "comparative": [
                r'\bdifference between\b',
                r'\bcompare\b',
                r'\bversus\b',
                r'\bvs\b',
                r'\bbetter than\b',
                r'\bsimilar to\b'
            ],
            "conceptual": [
                r'\bwhy does\b',
                r'\bwhy is\b',
                r'\bhow does\b.*\bwork\b',
                r'\bexplain\b',
                r'\bconcept of\b',
                r'\bprinciple of\b'
            ],
            "temporal": [
                r'\bwhen\b',
                r'\bbefore\b',
                r'\bafter\b',
                r'\bduring\b',
                r'\btimeline\b',
                r'\bhistory of\b'
            ],
            "causal": [
                r'\bwhy\b',
                r'\bcause of\b',
                r'\breason for\b',
                r'\bresult of\b',
                r'\bdue to\b',
                r'\bbecause of\b'
            ]
        }
    
    def _initialize_filter_extractors(self) -> Dict[str, List[callable]]:
        """Initialize filter extraction functions."""
        return {
            "author": [
                lambda q: re.findall(r'by ([A-Z][a-z]+ [A-Z][a-z]+)', q),
                lambda q: re.findall(r'author:(\w+)', q),
                lambda q: re.findall(r'written by ([^,\.]+)', q)
            ],
            "document_type": [
                lambda q: re.findall(r'type:(\w+)', q),
                lambda q: ['markdown'] if 'markdown' in q.lower() else [],
                lambda q: ['pdf'] if 'pdf' in q.lower() else []
            ],
            "category": [
                lambda q: re.findall(r'category:(\w+)', q),
                lambda q: re.findall(r'in category ([^,\.]+)', q)
            ],
            "tags": [
                lambda q: re.findall(r'tag:(\w+)', q),
                lambda q: re.findall(r'tagged with ([^,\.]+)', q)
            ],
            "language": [
                lambda q: re.findall(r'language:(\w+)', q),
                lambda q: re.findall(r'in (\w+) language', q)
            ]
        }
