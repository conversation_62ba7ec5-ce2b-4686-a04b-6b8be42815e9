"""
Main Bleen Agent implementation that orchestrates question answering.
"""

import time
from typing import Dict, List, Optional, Any
from datetime import datetime

from ..core.config import settings
from ..core.rag_engine import BleenRAGEngine
from ..core.query_analyzer import QueryAnalyzer, QueryAnalysis
from ..solr.client import SolrClient
from ..models.document import QueryContext, SearchResult
from ..utils.logging import AgentLogger

logger = AgentLogger(__name__)


class BleenAgent:
    """
    Main AI agent that handles document retrieval and question answering.
    """
    
    def __init__(self, solr_client: Optional[SolrClient] = None):
        """
        Initialize the Bleen Agent.
        
        Args:
            solr_client: Optional Solr client instance
        """
        logger.log_step("Initializing Bleen Agent", {})
        
        # Initialize components
        self.solr_client = solr_client or SolrClient()
        self.query_analyzer = QueryAnalyzer()
        self.rag_engine = BleenRAGEngine(self.solr_client)
        
        # Conversation state
        self.conversation_history: List[Dict[str, Any]] = []
        self.session_id = self._generate_session_id()
        
        # Verify Solr connection
        if not self.solr_client.ping():
            logger.error("Failed to connect to Solr", url=self.solr_client.full_url)
            raise ConnectionError(f"Cannot connect to Solr at {self.solr_client.full_url}")
        
        logger.log_step("Agent Initialization Complete", {
            "session_id": self.session_id,
            "solr_url": self.solr_client.full_url
        })
    
    def ask(self, question: str, context: Optional[Dict[str, Any]] = None) -> Dict[str, Any]:
        """
        Process a question and return an answer with supporting information.
        
        Args:
            question: User's question
            context: Optional context for the query (filters, preferences, etc.)
            
        Returns:
            Dictionary containing answer, sources, and metadata
        """
        start_time = time.time()
        
        logger.log_step("Processing Question", {
            "question": question,
            "session_id": self.session_id,
            "context": context or {}
        })
        
        try:
            # Analyze the query
            query_analysis = self.query_analyzer.analyze_query(question)
            
            # Apply context filters if provided
            filters = query_analysis.filters.copy()
            if context and 'filters' in context:
                filters.update(context['filters'])
            
            # Override search strategy if specified in context
            search_strategy = query_analysis.search_strategy
            if context and 'search_strategy' in context:
                search_strategy = context['search_strategy']
                logger.log_step("Search Strategy Override", {
                    "original": query_analysis.search_strategy.value,
                    "override": search_strategy
                })
            
            # Perform RAG query
            rag_result = self.rag_engine.query(
                query=question,
                search_strategy=search_strategy.value,
                max_results=context.get('max_results') if context else None,
                filters=filters
            )
            
            # Calculate processing time
            processing_time = time.time() - start_time
            
            # Create response
            response = {
                "answer": rag_result["answer"],
                "sources": rag_result["sources"],
                "confidence": rag_result["confidence"],
                "query_analysis": {
                    "query_type": query_analysis.query_type.value,
                    "search_strategy": search_strategy.value,
                    "key_terms": query_analysis.key_terms,
                    "filters": filters,
                    "reasoning": query_analysis.reasoning
                },
                "metadata": {
                    "session_id": self.session_id,
                    "processing_time_seconds": processing_time,
                    "timestamp": datetime.utcnow().isoformat(),
                    "num_sources": len(rag_result["sources"]),
                    "search_results_found": rag_result["query_context"].num_results_found
                }
            }
            
            # Add to conversation history
            self._add_to_history(question, response)
            
            logger.log_step("Question Processing Complete", {
                "processing_time": processing_time,
                "confidence": rag_result["confidence"],
                "num_sources": len(rag_result["sources"])
            })
            
            return response
            
        except Exception as e:
            processing_time = time.time() - start_time
            logger.error("Question processing failed", 
                        question=question, 
                        error=str(e),
                        processing_time=processing_time)
            
            return {
                "answer": f"I encountered an error while processing your question: {str(e)}",
                "sources": [],
                "confidence": 0.0,
                "query_analysis": None,
                "metadata": {
                    "session_id": self.session_id,
                    "processing_time_seconds": processing_time,
                    "timestamp": datetime.utcnow().isoformat(),
                    "error": str(e)
                }
            }
    
    def get_similar_documents(self, document_id: str, max_results: int = 5) -> Dict[str, Any]:
        """
        Find documents similar to a given document.
        
        Args:
            document_id: ID of the reference document
            max_results: Maximum number of similar documents to return
            
        Returns:
            Dictionary containing similar documents and metadata
        """
        logger.log_step("Finding Similar Documents", {
            "document_id": document_id,
            "max_results": max_results
        })
        
        try:
            similar_docs = self.rag_engine.get_similar_documents(document_id, max_results)
            
            response = {
                "similar_documents": self._format_similar_documents(similar_docs),
                "reference_document_id": document_id,
                "metadata": {
                    "session_id": self.session_id,
                    "timestamp": datetime.utcnow().isoformat(),
                    "num_similar": len(similar_docs)
                }
            }
            
            logger.log_step("Similar Documents Found", {
                "num_similar": len(similar_docs)
            })
            
            return response
            
        except Exception as e:
            logger.error("Similar document search failed", 
                        document_id=document_id, 
                        error=str(e))
            
            return {
                "similar_documents": [],
                "reference_document_id": document_id,
                "metadata": {
                    "session_id": self.session_id,
                    "timestamp": datetime.utcnow().isoformat(),
                    "error": str(e)
                }
            }
    
    def get_conversation_history(self) -> List[Dict[str, Any]]:
        """Get the conversation history for this session."""
        return self.conversation_history.copy()
    
    def clear_conversation_history(self) -> None:
        """Clear the conversation history."""
        logger.log_step("Clearing Conversation History", {
            "previous_length": len(self.conversation_history)
        })
        self.conversation_history.clear()
    
    def get_collection_stats(self) -> Dict[str, Any]:
        """Get statistics about the document collection."""
        logger.log_step("Getting Collection Statistics", {})
        
        try:
            stats = self.solr_client.get_collection_stats()
            
            enhanced_stats = {
                **stats,
                "session_id": self.session_id,
                "timestamp": datetime.utcnow().isoformat(),
                "agent_version": "0.1.0"
            }
            
            logger.log_step("Collection Statistics Retrieved", {
                "total_documents": stats.get('total_documents', 0)
            })
            
            return enhanced_stats
            
        except Exception as e:
            logger.error("Failed to get collection stats", error=str(e))
            return {
                "error": str(e),
                "session_id": self.session_id,
                "timestamp": datetime.utcnow().isoformat()
            }
    
    def search_documents(
        self, 
        query: str, 
        search_type: str = "hybrid",
        filters: Optional[Dict[str, Any]] = None,
        max_results: int = 10
    ) -> Dict[str, Any]:
        """
        Search documents directly without answer generation.
        
        Args:
            query: Search query
            search_type: Type of search ("fulltext", "vector", "hybrid")
            filters: Optional filters
            max_results: Maximum results to return
            
        Returns:
            Search results and metadata
        """
        logger.log_step("Direct Document Search", {
            "query": query,
            "search_type": search_type,
            "filters": filters or {},
            "max_results": max_results
        })
        
        try:
            # Perform search based on type
            if search_type == "fulltext":
                results = self.solr_client.search_fulltext(query, filters, max_results)
            elif search_type == "vector":
                results = self.solr_client.search_vector(query, filters, max_results)
            elif search_type == "hybrid":
                results = self.solr_client.search_hybrid(query, filters, max_results)
            else:
                raise ValueError(f"Unknown search type: {search_type}")
            
            response = {
                "results": [self._format_search_result(result) for result in results],
                "query": query,
                "search_type": search_type,
                "filters": filters or {},
                "metadata": {
                    "session_id": self.session_id,
                    "timestamp": datetime.utcnow().isoformat(),
                    "num_results": len(results),
                    "max_results": max_results
                }
            }
            
            logger.log_step("Document Search Complete", {
                "num_results": len(results)
            })
            
            return response
            
        except Exception as e:
            logger.error("Document search failed", 
                        query=query, 
                        search_type=search_type,
                        error=str(e))
            
            return {
                "results": [],
                "query": query,
                "search_type": search_type,
                "filters": filters or {},
                "metadata": {
                    "session_id": self.session_id,
                    "timestamp": datetime.utcnow().isoformat(),
                    "error": str(e)
                }
            }
    
    def _generate_session_id(self) -> str:
        """Generate a unique session ID."""
        return f"session_{int(time.time())}_{hash(str(datetime.utcnow())) % 10000}"
    
    def _add_to_history(self, question: str, response: Dict[str, Any]) -> None:
        """Add a question-answer pair to conversation history."""
        history_entry = {
            "question": question,
            "answer": response["answer"],
            "confidence": response["confidence"],
            "timestamp": response["metadata"]["timestamp"],
            "num_sources": response["metadata"]["num_sources"]
        }
        
        self.conversation_history.append(history_entry)
        
        # Keep only last 50 entries to prevent memory issues
        if len(self.conversation_history) > 50:
            self.conversation_history = self.conversation_history[-50:]
    
    def _format_similar_documents(self, search_results: List[SearchResult]) -> List[Dict[str, Any]]:
        """Format similar documents for response."""
        return [self._format_search_result(result) for result in search_results]
    
    def _format_search_result(self, result: SearchResult) -> Dict[str, Any]:
        """Format a search result for response."""
        return {
            "id": result.id,
            "document_id": result.document_id,
            "title": result.title or "Untitled",
            "content_preview": result.content[:300] + "..." if len(result.content) > 300 else result.content,
            "relevance_score": result.score,
            "is_chunk": result.is_chunk,
            "metadata": result.metadata,
            "search_type": result.search_type
        }
