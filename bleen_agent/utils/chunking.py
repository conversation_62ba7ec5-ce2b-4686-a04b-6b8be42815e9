"""
Intelligent document chunking strategies for optimal retrieval performance.
"""

import re
import tiktoken
from typing import List, <PERSON><PERSON>, Optional
from dataclasses import dataclass

from ..core.config import settings
from ..models.document import Document, DocumentChunk, DocumentType


@dataclass
class ChunkingConfig:
    """Configuration for document chunking."""
    max_chunk_size: int = settings.max_chunk_size
    chunk_overlap: int = settings.chunk_overlap
    min_chunk_size: int = 100
    preserve_sentences: bool = True
    preserve_paragraphs: bool = True
    respect_markdown_structure: bool = True


class DocumentChunker:
    """
    Intelligent document chunker that adapts strategy based on document type and content.
    """
    
    def __init__(self, config: Optional[ChunkingConfig] = None):
        self.config = config or ChunkingConfig()
        self.tokenizer = tiktoken.get_encoding("cl100k_base")  # GPT-4 tokenizer
    
    def chunk_document(self, document: Document) -> List[DocumentChunk]:
        """
        Chunk a document using the most appropriate strategy.
        
        Args:
            document: Document to chunk
            
        Returns:
            List of document chunks
        """
        if document.document_type == DocumentType.MARKDOWN:
            return self._chunk_markdown(document)
        else:
            return self._chunk_text(document)
    
    def _chunk_markdown(self, document: Document) -> List[DocumentChunk]:
        """
        Chunk markdown document respecting structure (headers, paragraphs, etc.).
        """
        chunks = []
        content = document.content
        
        # Split by major sections (headers)
        sections = self._split_by_headers(content)
        
        for section_idx, (header, section_content) in enumerate(sections):
            if not section_content.strip():
                continue
                
            # If section is small enough, keep as single chunk
            if self._count_tokens(section_content) <= self.config.max_chunk_size:
                chunk = self._create_chunk(
                    document=document,
                    content=f"{header}\n{section_content}" if header else section_content,
                    chunk_index=len(chunks),
                    start_char=0,  # TODO: Calculate actual positions
                    end_char=len(section_content)
                )
                chunks.append(chunk)
            else:
                # Split large sections into smaller chunks
                section_chunks = self._chunk_text_content(
                    content=section_content,
                    document=document,
                    start_chunk_index=len(chunks),
                    prefix=header
                )
                chunks.extend(section_chunks)
        
        # Link chunks for context
        self._link_chunks(chunks)
        return chunks
    
    def _chunk_text(self, document: Document) -> List[DocumentChunk]:
        """
        Chunk plain text document using sentence and paragraph boundaries.
        """
        chunks = self._chunk_text_content(
            content=document.content,
            document=document,
            start_chunk_index=0
        )
        self._link_chunks(chunks)
        return chunks
    
    def _chunk_text_content(
        self, 
        content: str, 
        document: Document, 
        start_chunk_index: int = 0,
        prefix: str = ""
    ) -> List[DocumentChunk]:
        """
        Core text chunking logic that respects sentence and paragraph boundaries.
        """
        chunks = []
        
        # Split into paragraphs first
        paragraphs = self._split_paragraphs(content)
        
        current_chunk = prefix + "\n" if prefix else ""
        current_start = 0
        
        for para in paragraphs:
            para_tokens = self._count_tokens(para)
            current_tokens = self._count_tokens(current_chunk)
            
            # If adding this paragraph would exceed max size
            if current_tokens + para_tokens > self.config.max_chunk_size and current_chunk.strip():
                # Save current chunk
                chunk = self._create_chunk(
                    document=document,
                    content=current_chunk.strip(),
                    chunk_index=start_chunk_index + len(chunks),
                    start_char=current_start,
                    end_char=current_start + len(current_chunk)
                )
                chunks.append(chunk)
                
                # Start new chunk with overlap
                overlap_content = self._get_overlap_content(current_chunk)
                current_chunk = overlap_content + para
                current_start += len(current_chunk) - len(overlap_content)
            else:
                # Add paragraph to current chunk
                if current_chunk and not current_chunk.endswith('\n'):
                    current_chunk += '\n'
                current_chunk += para
        
        # Add final chunk if it has content
        if current_chunk.strip():
            chunk = self._create_chunk(
                document=document,
                content=current_chunk.strip(),
                chunk_index=start_chunk_index + len(chunks),
                start_char=current_start,
                end_char=current_start + len(current_chunk)
            )
            chunks.append(chunk)
        
        return chunks
    
    def _split_by_headers(self, content: str) -> List[Tuple[str, str]]:
        """Split markdown content by headers."""
        sections = []
        lines = content.split('\n')
        current_header = ""
        current_content = []
        
        for line in lines:
            if re.match(r'^#{1,6}\s+', line):  # Markdown header
                if current_content:
                    sections.append((current_header, '\n'.join(current_content)))
                current_header = line
                current_content = []
            else:
                current_content.append(line)
        
        # Add final section
        if current_content:
            sections.append((current_header, '\n'.join(current_content)))
        
        return sections
    
    def _split_paragraphs(self, content: str) -> List[str]:
        """Split content into paragraphs."""
        # Split by double newlines (paragraph breaks)
        paragraphs = re.split(r'\n\s*\n', content)
        return [p.strip() for p in paragraphs if p.strip()]
    
    def _count_tokens(self, text: str) -> int:
        """Count tokens in text using the tokenizer."""
        return len(self.tokenizer.encode(text))
    
    def _get_overlap_content(self, chunk: str) -> str:
        """Get overlap content from the end of a chunk."""
        if not self.config.chunk_overlap:
            return ""
        
        # Try to get last few sentences for overlap
        sentences = re.split(r'[.!?]+', chunk)
        overlap_sentences = []
        overlap_tokens = 0
        
        for sentence in reversed(sentences):
            sentence = sentence.strip()
            if not sentence:
                continue
                
            sentence_tokens = self._count_tokens(sentence)
            if overlap_tokens + sentence_tokens > self.config.chunk_overlap:
                break
                
            overlap_sentences.insert(0, sentence)
            overlap_tokens += sentence_tokens
        
        return '. '.join(overlap_sentences) + '. ' if overlap_sentences else ""
    
    def _create_chunk(
        self, 
        document: Document, 
        content: str, 
        chunk_index: int,
        start_char: int,
        end_char: int
    ) -> DocumentChunk:
        """Create a DocumentChunk from content."""
        chunk_id = f"{document.document_id}_chunk_{chunk_index}"
        
        return DocumentChunk(
            chunk_id=chunk_id,
            document_id=document.document_id,
            chunk_index=chunk_index,
            content=content,
            title=document.title,
            author=document.author,
            category=document.category,
            tags=document.tags,
            language=document.language,
            document_type=document.document_type,
            source_file=document.source_file,
            file_path=document.file_path,
            start_char=start_char,
            end_char=end_char
        )
    
    def _link_chunks(self, chunks: List[DocumentChunk]) -> None:
        """Link chunks together for context navigation."""
        for i, chunk in enumerate(chunks):
            if i > 0:
                chunk.previous_chunk_id = chunks[i-1].chunk_id
            if i < len(chunks) - 1:
                chunk.next_chunk_id = chunks[i+1].chunk_id


def should_chunk_document(document: Document) -> bool:
    """
    Determine if a document should be chunked based on its size and type.
    
    Args:
        document: Document to evaluate
        
    Returns:
        True if document should be chunked
    """
    # Always chunk if document is very large
    if len(document.content) > settings.max_chunk_size * 2:
        return True
    
    # Chunk markdown documents with multiple sections
    if document.document_type == DocumentType.MARKDOWN:
        header_count = len(re.findall(r'^#{1,6}\s+', document.content, re.MULTILINE))
        if header_count > 3:
            return True
    
    # Don't chunk small documents
    if len(document.content) < settings.max_chunk_size:
        return False
    
    return True
