"""
Structured logging configuration for <PERSON><PERSON> Agent.
"""

import sys
import structlog
from typing import Any, Dict
from rich.console import Console
from rich.logging import <PERSON><PERSON><PERSON><PERSON>

from ..core.config import settings


def setup_logging() -> None:
    """Configure structured logging with Rich output."""
    
    # Configure structlog
    structlog.configure(
        processors=[
            structlog.stdlib.filter_by_level,
            structlog.stdlib.add_logger_name,
            structlog.stdlib.add_log_level,
            structlog.stdlib.PositionalArgumentsFormatter(),
            structlog.processors.TimeStamper(fmt="iso"),
            structlog.processors.StackInfoRenderer(),
            structlog.processors.format_exc_info,
            structlog.processors.UnicodeDecoder(),
            structlog.processors.JSONRenderer()
        ],
        context_class=dict,
        logger_factory=structlog.stdlib.LoggerFactory(),
        wrapper_class=structlog.stdlib.BoundLogger,
        cache_logger_on_first_use=True,
    )


def get_logger(name: str) -> structlog.BoundLogger:
    """Get a structured logger instance."""
    return structlog.get_logger(name)


class AgentLogger:
    """Enhanced logger for agent operations with detailed step tracking."""
    
    def __init__(self, name: str):
        self.logger = get_logger(name)
        self.console = Console()
        self.step_counter = 0
    
    def log_step(self, step_name: str, details: Dict[str, Any] = None) -> None:
        """Log a step in the agent's reasoning process."""
        self.step_counter += 1
        step_info = {
            "step": self.step_counter,
            "step_name": step_name,
            "details": details or {}
        }
        
        self.logger.info("Agent Step", **step_info)
        
        # Also print to console for immediate feedback
        self.console.print(f"[bold blue]Step {self.step_counter}:[/bold blue] {step_name}")
        if details:
            for key, value in details.items():
                self.console.print(f"  [dim]{key}:[/dim] {value}")
    
    def log_search_query(self, query_type: str, query: str, filters: Dict[str, Any] = None) -> None:
        """Log search query construction."""
        self.log_step(
            "Search Query Construction",
            {
                "query_type": query_type,
                "query": query,
                "filters": filters or {}
            }
        )
    
    def log_retrieval_results(self, num_results: int, sources: list = None) -> None:
        """Log document retrieval results."""
        self.log_step(
            "Document Retrieval",
            {
                "num_results": num_results,
                "sources": sources or []
            }
        )
    
    def log_answer_generation(self, context_length: int, answer_preview: str) -> None:
        """Log answer generation process."""
        self.log_step(
            "Answer Generation",
            {
                "context_length": context_length,
                "answer_preview": answer_preview[:100] + "..." if len(answer_preview) > 100 else answer_preview
            }
        )
    
    def info(self, message: str, **kwargs) -> None:
        """Log info message."""
        self.logger.info(message, **kwargs)
    
    def error(self, message: str, **kwargs) -> None:
        """Log error message."""
        self.logger.error(message, **kwargs)
    
    def warning(self, message: str, **kwargs) -> None:
        """Log warning message."""
        self.logger.warning(message, **kwargs)
    
    def debug(self, message: str, **kwargs) -> None:
        """Log debug message."""
        self.logger.debug(message, **kwargs)
