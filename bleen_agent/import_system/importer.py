"""
Document importer that handles parsing, chunking, and indexing documents into Solr.
"""

import os
from pathlib import Path
from typing import List, Optional, Dict, Any

from ..models.document import Document, DocumentChunk
from ..solr.client import SolrClient
from ..utils.chunking import Document<PERSON>hunker, should_chunk_document
from ..utils.logging import AgentLogger
from .parser import DocumentParser, DirectoryScanner

logger = AgentLogger(__name__)


class DocumentImporter:
    """
    Main document importer that orchestrates parsing, chunking, and indexing.
    """
    
    def __init__(self, solr_client: Optional[SolrClient] = None):
        self.solr_client = solr_client or SolrClient()
        self.parser = DocumentParser()
        self.chunker = DocumentChunker()
        self.scanner = DirectoryScanner()
    
    def import_file(self, file_path: str, chunk_documents: bool = True) -> bool:
        """
        Import a single file into the system.
        
        Args:
            file_path: Path to the file to import
            chunk_documents: Whether to chunk large documents
            
        Returns:
            True if successful
        """
        logger.log_step("Starting File Import", {"file_path": file_path})
        
        try:
            # Parse the document
            document = self.parser.parse_file(file_path)
            if not document:
                logger.error("Failed to parse document", file_path=file_path)
                return False
            
            logger.log_step("Document Parsed", {
                "document_id": document.document_id,
                "title": document.title,
                "content_length": document.content_length,
                "word_count": document.word_count
            })
            
            # Decide whether to chunk
            should_chunk = chunk_documents and should_chunk_document(document)
            
            if should_chunk:
                return self._import_with_chunking(document)
            else:
                return self._import_whole_document(document)
                
        except Exception as e:
            logger.error("Import failed", file_path=file_path, error=str(e))
            return False
    
    def import_directory(
        self, 
        directory: str, 
        recursive: bool = True,
        chunk_documents: bool = True,
        batch_size: int = 10
    ) -> Dict[str, Any]:
        """
        Import all documents from a directory.
        
        Args:
            directory: Directory path to import from
            recursive: Whether to scan subdirectories
            chunk_documents: Whether to chunk large documents
            batch_size: Number of documents to process in each batch
            
        Returns:
            Import statistics
        """
        logger.log_step("Starting Directory Import", {
            "directory": directory,
            "recursive": recursive,
            "chunk_documents": chunk_documents
        })
        
        # Scan directory for documents
        documents = self.scanner.scan_directory(directory, recursive)
        
        if not documents:
            logger.warning("No documents found in directory", directory=directory)
            return {"total_files": 0, "successful": 0, "failed": 0, "chunks_created": 0}
        
        logger.log_step("Documents Found", {"count": len(documents)})
        
        # Import documents in batches
        stats = {
            "total_files": len(documents),
            "successful": 0,
            "failed": 0,
            "chunks_created": 0
        }
        
        for i in range(0, len(documents), batch_size):
            batch = documents[i:i + batch_size]
            batch_stats = self._import_batch(batch, chunk_documents)
            
            stats["successful"] += batch_stats["successful"]
            stats["failed"] += batch_stats["failed"]
            stats["chunks_created"] += batch_stats["chunks_created"]
            
            logger.log_step("Batch Processed", {
                "batch_number": (i // batch_size) + 1,
                "batch_size": len(batch),
                "successful": batch_stats["successful"],
                "failed": batch_stats["failed"]
            })
        
        logger.log_step("Directory Import Completed", stats)
        return stats
    
    def _import_batch(self, documents: List[Document], chunk_documents: bool) -> Dict[str, Any]:
        """Import a batch of documents."""
        stats = {"successful": 0, "failed": 0, "chunks_created": 0}
        
        for document in documents:
            try:
                should_chunk = chunk_documents and should_chunk_document(document)
                
                if should_chunk:
                    success = self._import_with_chunking(document)
                    if success:
                        # Count chunks created
                        chunks = self.chunker.chunk_document(document)
                        stats["chunks_created"] += len(chunks)
                else:
                    success = self._import_whole_document(document)
                
                if success:
                    stats["successful"] += 1
                else:
                    stats["failed"] += 1
                    
            except Exception as e:
                logger.error("Failed to import document", 
                           document_id=document.document_id, 
                           error=str(e))
                stats["failed"] += 1
        
        return stats
    
    def _import_whole_document(self, document: Document) -> bool:
        """Import a document as a single entity."""
        logger.log_step("Importing Whole Document", {
            "document_id": document.document_id,
            "strategy": "whole_document"
        })
        
        success = self.solr_client.index_document(document)
        
        if success:
            logger.log_step("Document Indexed Successfully", {
                "document_id": document.document_id,
                "type": "whole_document"
            })
        
        return success
    
    def _import_with_chunking(self, document: Document) -> bool:
        """Import a document with chunking."""
        logger.log_step("Importing with Chunking", {
            "document_id": document.document_id,
            "strategy": "chunked"
        })
        
        try:
            # Create chunks
            chunks = self.chunker.chunk_document(document)
            
            logger.log_step("Document Chunked", {
                "document_id": document.document_id,
                "num_chunks": len(chunks)
            })
            
            # Index the original document (for metadata and full-text search)
            document_success = self.solr_client.index_document(document)
            
            if not document_success:
                logger.error("Failed to index original document", 
                           document_id=document.document_id)
                return False
            
            # Index all chunks
            chunk_failures = 0
            for chunk in chunks:
                chunk_success = self.solr_client.index_chunk(chunk)
                if not chunk_success:
                    chunk_failures += 1
                    logger.error("Failed to index chunk", 
                               chunk_id=chunk.chunk_id)
            
            if chunk_failures > 0:
                logger.warning("Some chunks failed to index", 
                             document_id=document.document_id,
                             failed_chunks=chunk_failures,
                             total_chunks=len(chunks))
            
            success = chunk_failures == 0
            
            if success:
                logger.log_step("Chunked Document Indexed Successfully", {
                    "document_id": document.document_id,
                    "chunks_created": len(chunks)
                })
            
            return success
            
        except Exception as e:
            logger.error("Chunking import failed", 
                        document_id=document.document_id, 
                        error=str(e))
            return False
    
    def delete_document(self, document_id: str) -> bool:
        """Delete a document and all its chunks from the index."""
        logger.log_step("Deleting Document", {"document_id": document_id})
        
        success = self.solr_client.delete_document(document_id)
        
        if success:
            logger.log_step("Document Deleted Successfully", {"document_id": document_id})
        
        return success
    
    def get_import_status(self) -> Dict[str, Any]:
        """Get current import status and collection statistics."""
        logger.log_step("Getting Import Status", {})
        
        stats = self.solr_client.get_collection_stats()
        
        logger.log_step("Import Status Retrieved", stats)
        
        return stats
    
    def validate_import(self, document_ids: List[str]) -> Dict[str, bool]:
        """Validate that documents were imported successfully."""
        logger.log_step("Validating Import", {"document_count": len(document_ids)})
        
        validation_results = {}
        
        for doc_id in document_ids:
            doc = self.solr_client.get_document(doc_id)
            validation_results[doc_id] = doc is not None
        
        successful_validations = sum(1 for result in validation_results.values() if result)
        
        logger.log_step("Import Validation Completed", {
            "total_documents": len(document_ids),
            "successful_validations": successful_validations,
            "failed_validations": len(document_ids) - successful_validations
        })
        
        return validation_results
