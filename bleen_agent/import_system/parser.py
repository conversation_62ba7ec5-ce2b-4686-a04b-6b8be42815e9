"""
Document parser for extracting structured information from various file formats.
"""

import os
import re
import hashlib
from pathlib import Path
from typing import Dict, List, Optional, Any, Tuple
from datetime import datetime

import frontmatter
import markdown

from ..models.document import Document, DocumentType
from ..utils.logging import get_logger

logger = get_logger(__name__)


class DocumentParser:
    """
    Parser for extracting structured information from documents.
    """
    
    def __init__(self):
        self.markdown_parser = markdown.Markdown(
            extensions=['meta', 'toc', 'tables', 'fenced_code']
        )
    
    def parse_file(self, file_path: str) -> Optional[Document]:
        """
        Parse a file and extract document information.
        
        Args:
            file_path: Path to the file to parse
            
        Returns:
            Document object or None if parsing fails
        """
        try:
            file_path = Path(file_path)
            
            if not file_path.exists():
                logger.error("File not found", file_path=str(file_path))
                return None
            
            # Determine document type
            doc_type = self._detect_document_type(file_path)
            
            # Parse based on type
            if doc_type == DocumentType.MARKDOWN:
                return self._parse_markdown(file_path)
            elif doc_type == DocumentType.TEXT:
                return self._parse_text(file_path)
            else:
                logger.warning("Unsupported document type", 
                             file_path=str(file_path), 
                             doc_type=doc_type)
                return None
                
        except Exception as e:
            logger.error("Failed to parse file", 
                        file_path=str(file_path), 
                        error=str(e))
            return None
    
    def _detect_document_type(self, file_path: Path) -> DocumentType:
        """Detect document type based on file extension."""
        extension = file_path.suffix.lower()
        
        if extension in ['.md', '.markdown']:
            return DocumentType.MARKDOWN
        elif extension in ['.txt']:
            return DocumentType.TEXT
        else:
            return DocumentType.UNKNOWN
    
    def _parse_markdown(self, file_path: Path) -> Document:
        """Parse a Markdown file with frontmatter support."""
        with open(file_path, 'r', encoding='utf-8') as f:
            content = f.read()
        
        # Parse frontmatter
        post = frontmatter.loads(content)
        
        # Extract metadata from frontmatter
        metadata = post.metadata
        content_body = post.content
        
        # Generate document ID
        document_id = self._generate_document_id(file_path, content_body)
        
        # Extract title (from frontmatter or first header)
        title = metadata.get('title') or self._extract_title_from_content(content_body)
        
        # Extract other metadata
        author = self._normalize_list_field(metadata.get('author') or metadata.get('authors'))
        category = self._normalize_list_field(metadata.get('category') or metadata.get('categories'))
        tags = self._normalize_list_field(metadata.get('tags') or metadata.get('tag'))
        keywords = self._normalize_list_field(metadata.get('keywords'))
        
        # Get file stats
        file_stats = file_path.stat()
        created_date = datetime.fromtimestamp(file_stats.st_ctime)
        modified_date = datetime.fromtimestamp(file_stats.st_mtime)
        
        # Calculate content metrics
        word_count = len(content_body.split())
        content_length = len(content_body)
        
        # Extract custom fields (everything not in standard fields)
        standard_fields = {
            'title', 'author', 'authors', 'category', 'categories', 
            'tags', 'tag', 'keywords', 'summary', 'description', 'language'
        }
        custom_fields = {
            k: v for k, v in metadata.items() 
            if k not in standard_fields
        }
        
        return Document(
            document_id=document_id,
            title=title,
            content=content_body,
            summary=metadata.get('summary') or metadata.get('description'),
            author=author,
            category=category,
            tags=tags,
            keywords=keywords,
            language=metadata.get('language', 'en'),
            document_type=DocumentType.MARKDOWN,
            source_file=file_path.name,
            file_path=str(file_path),
            created_date=created_date,
            modified_date=modified_date,
            content_length=content_length,
            word_count=word_count,
            custom_fields=custom_fields
        )
    
    def _parse_text(self, file_path: Path) -> Document:
        """Parse a plain text file."""
        with open(file_path, 'r', encoding='utf-8') as f:
            content = f.read()
        
        # Generate document ID
        document_id = self._generate_document_id(file_path, content)
        
        # Try to extract title from first line if it looks like a title
        title = self._extract_title_from_content(content)
        
        # Get file stats
        file_stats = file_path.stat()
        created_date = datetime.fromtimestamp(file_stats.st_ctime)
        modified_date = datetime.fromtimestamp(file_stats.st_mtime)
        
        # Calculate content metrics
        word_count = len(content.split())
        content_length = len(content)
        
        return Document(
            document_id=document_id,
            title=title or file_path.stem,
            content=content,
            language='en',
            document_type=DocumentType.TEXT,
            source_file=file_path.name,
            file_path=str(file_path),
            created_date=created_date,
            modified_date=modified_date,
            content_length=content_length,
            word_count=word_count
        )
    
    def _generate_document_id(self, file_path: Path, content: str) -> str:
        """Generate a unique document ID based on file path and content."""
        # Use file path and content hash for uniqueness
        content_hash = hashlib.md5(content.encode('utf-8')).hexdigest()[:8]
        file_hash = hashlib.md5(str(file_path).encode('utf-8')).hexdigest()[:8]
        return f"doc_{file_hash}_{content_hash}"
    
    def _extract_title_from_content(self, content: str) -> Optional[str]:
        """Extract title from content (first header or first line)."""
        lines = content.strip().split('\n')
        
        if not lines:
            return None
        
        first_line = lines[0].strip()
        
        # Check if first line is a markdown header
        header_match = re.match(r'^#{1,6}\s+(.+)', first_line)
        if header_match:
            return header_match.group(1).strip()
        
        # Check if first line looks like a title (short, no punctuation at end)
        if len(first_line) < 100 and not first_line.endswith('.'):
            return first_line
        
        return None
    
    def _normalize_list_field(self, value: Any) -> List[str]:
        """Normalize a field that can be a string or list to a list of strings."""
        if not value:
            return []
        
        if isinstance(value, str):
            # Split by comma if it contains commas
            if ',' in value:
                return [item.strip() for item in value.split(',') if item.strip()]
            else:
                return [value.strip()]
        elif isinstance(value, list):
            return [str(item).strip() for item in value if str(item).strip()]
        else:
            return [str(value).strip()]


class DirectoryScanner:
    """
    Scanner for finding and processing documents in directories.
    """
    
    def __init__(self, supported_extensions: List[str] = None):
        self.supported_extensions = supported_extensions or ['.md', '.markdown', '.txt']
        self.parser = DocumentParser()
    
    def scan_directory(self, directory: str, recursive: bool = True) -> List[Document]:
        """
        Scan a directory for documents and parse them.
        
        Args:
            directory: Directory path to scan
            recursive: Whether to scan subdirectories
            
        Returns:
            List of parsed documents
        """
        directory = Path(directory)
        
        if not directory.exists() or not directory.is_dir():
            logger.error("Directory not found or not a directory", directory=str(directory))
            return []
        
        documents = []
        
        # Find all supported files
        pattern = "**/*" if recursive else "*"
        
        for file_path in directory.glob(pattern):
            if file_path.is_file() and file_path.suffix.lower() in self.supported_extensions:
                logger.info("Processing file", file_path=str(file_path))
                
                document = self.parser.parse_file(file_path)
                if document:
                    documents.append(document)
                else:
                    logger.warning("Failed to parse file", file_path=str(file_path))
        
        logger.info("Directory scan completed", 
                   directory=str(directory), 
                   files_found=len(documents))
        
        return documents
