"""
Analysis tools for Bleen Agent.

Provides tools for:
- Query analysis and search strategy recommendation
- Collection statistics and health checks
- Content analysis and insights
- Search performance analysis
"""

import re
import json
from typing import Dict, List, Optional, Any, Tuple
from datetime import datetime
from collections import Counter

import pysolr
from llama_index.core.tools import BaseTool

from ..core.config import settings
from ..utils.logging import get_logger

logger = get_logger(__name__)


class QueryAnalysisTool(BaseTool):
    """
    Tool for analyzing user queries and recommending search strategies.
    
    Analyzes queries to:
    - Classify query type (factual, conceptual, procedural, etc.)
    - Extract key terms and entities
    - Recommend optimal search strategy
    - Suggest filters and refinements
    """
    
    def __init__(self):
        metadata = {
            "name": "analyze_query",
            "description": (
                "Analyze a user query to understand its intent, extract key terms, "
                "classify the query type, and recommend the best search strategy. "
                "Helps optimize search results by understanding what the user is looking for."
            )
        }
        super().__init__(metadata=metadata)
        
        # Query classification patterns
        self.query_patterns = {
            "definitional": [
                r'\bwhat is\b', r'\bwhat are\b', r'\bdefine\b', r'\bdefinition of\b',
                r'\bmeaning of\b', r'\bexplain what\b'
            ],
            "factual": [
                r'\bwhen did\b', r'\bwhere is\b', r'\bwho is\b', r'\bhow many\b',
                r'\bhow much\b', r'\bwhat year\b', r'\bwhich\b.*\bis\b'
            ],
            "procedural": [
                r'\bhow to\b', r'\bhow do i\b', r'\bhow can i\b', r'\bsteps to\b',
                r'\bprocess of\b', r'\bway to\b', r'\bguide to\b'
            ],
            "comparative": [
                r'\bdifference between\b', r'\bcompare\b', r'\bversus\b', r'\bvs\b',
                r'\bbetter than\b', r'\bsimilar to\b', r'\bcontrast\b'
            ],
            "conceptual": [
                r'\bwhy does\b', r'\bwhy is\b', r'\bhow does\b.*\bwork\b',
                r'\bexplain\b', r'\bconcept of\b', r'\bprinciple of\b'
            ],
            "temporal": [
                r'\bwhen\b', r'\bbefore\b', r'\bafter\b', r'\bduring\b',
                r'\btimeline\b', r'\bhistory of\b', r'\bevolution of\b'
            ],
            "causal": [
                r'\bwhy\b', r'\bcause of\b', r'\breason for\b', r'\bresult of\b',
                r'\bdue to\b', r'\bbecause of\b', r'\bimpact of\b'
            ]
        }
        
        # Filter extraction patterns
        self.filter_patterns = {
            "author": [
                r'by ([A-Z][a-z]+ [A-Z][a-z]+)',
                r'author:(\w+)',
                r'written by ([^,\.]+)'
            ],
            "document_type": [
                r'type:(\w+)',
                r'\b(markdown|pdf|text|docx)\b'
            ],
            "category": [
                r'category:(\w+)',
                r'in category ([^,\.]+)'
            ],
            "tags": [
                r'tag:(\w+)',
                r'tagged with ([^,\.]+)'
            ]
        }
    
    def call(self, query: str, **kwargs) -> str:
        """
        Analyze a user query.
        
        Args:
            query: The user's query to analyze
            
        Returns:
            JSON string with query analysis results
        """
        try:
            logger.info("Analyzing query", query=query)
            
            # Clean and preprocess query
            processed_query = self._preprocess_query(query)
            
            # Classify query type
            query_type = self._classify_query_type(processed_query)
            
            # Extract key terms
            key_terms = self._extract_key_terms(processed_query)
            
            # Extract potential filters
            filters = self._extract_filters(processed_query)
            
            # Recommend search strategy
            search_strategy, confidence, reasoning = self._recommend_search_strategy(
                processed_query, query_type, key_terms, filters
            )
            
            # Generate search suggestions
            suggestions = self._generate_search_suggestions(
                processed_query, query_type, key_terms
            )
            
            analysis = {
                "original_query": query,
                "processed_query": processed_query,
                "query_type": query_type,
                "key_terms": key_terms,
                "extracted_filters": filters,
                "recommended_strategy": {
                    "strategy": search_strategy,
                    "confidence": confidence,
                    "reasoning": reasoning
                },
                "search_suggestions": suggestions,
                "query_complexity": self._assess_complexity(processed_query, key_terms),
                "analysis_timestamp": datetime.utcnow().isoformat()
            }
            
            logger.info("Query analysis completed", 
                       query_type=query_type,
                       strategy=search_strategy,
                       confidence=confidence)
            
            return json.dumps(analysis, indent=2)
            
        except Exception as e:
            logger.error("Query analysis failed", query=query, error=str(e))
            return json.dumps({
                "error": f"Query analysis failed: {str(e)}",
                "original_query": query
            })
    
    def _preprocess_query(self, query: str) -> str:
        """Clean and preprocess the query."""
        # Remove extra whitespace
        query = re.sub(r'\s+', ' ', query.strip())
        return query
    
    def _classify_query_type(self, query: str) -> str:
        """Classify the type of query."""
        query_lower = query.lower()
        
        for query_type, patterns in self.query_patterns.items():
            for pattern in patterns:
                if re.search(pattern, query_lower):
                    return query_type
        
        return "exploratory"
    
    def _extract_key_terms(self, query: str) -> List[str]:
        """Extract key terms from the query."""
        # Stop words to filter out
        stop_words = {
            'what', 'how', 'when', 'where', 'why', 'who', 'which', 'whose',
            'is', 'are', 'was', 'were', 'be', 'been', 'being',
            'the', 'a', 'an', 'and', 'or', 'but', 'in', 'on', 'at', 'to', 'for',
            'of', 'with', 'by', 'about', 'can', 'could', 'should', 'would',
            'do', 'does', 'did', 'have', 'has', 'had', 'will', 'would'
        }
        
        # Extract words
        words = re.findall(r'\b\w+\b', query.lower())
        
        # Filter out stop words and short words
        key_terms = [word for word in words if word not in stop_words and len(word) > 2]
        
        # Extract quoted phrases
        quoted_phrases = re.findall(r'"([^"]+)"', query)
        key_terms.extend(quoted_phrases)
        
        return key_terms
    
    def _extract_filters(self, query: str) -> Dict[str, List[str]]:
        """Extract potential filters from the query."""
        filters = {}
        
        for filter_type, patterns in self.filter_patterns.items():
            matches = []
            for pattern in patterns:
                found = re.findall(pattern, query, re.IGNORECASE)
                matches.extend(found)
            
            if matches:
                filters[filter_type] = matches
        
        return filters
    
    def _recommend_search_strategy(
        self, 
        query: str, 
        query_type: str, 
        key_terms: List[str],
        filters: Dict[str, List[str]]
    ) -> Tuple[str, float, str]:
        """Recommend the best search strategy."""
        
        # If we have filters, recommend filtered search
        if filters:
            return (
                "filtered_search",
                0.9,
                "Query contains specific filters that can narrow down results effectively"
            )
        
        # Strategy based on query type
        if query_type == "definitional":
            return (
                "document_search",
                0.8,
                "Definitional queries work best with full-text search for exact matches"
            )
        
        elif query_type == "factual":
            return (
                "document_search",
                0.8,
                "Factual queries benefit from keyword-based search"
            )
        
        elif query_type == "procedural":
            return (
                "document_search",
                0.7,
                "Procedural queries often contain specific steps and keywords"
            )
        
        elif query_type == "comparative":
            return (
                "document_search",
                0.7,
                "Comparative queries work well with keyword search for multiple concepts"
            )
        
        # Strategy based on query characteristics
        if len(key_terms) > 5:
            return (
                "document_search",
                0.6,
                "Complex queries with many terms work well with full-text search"
            )
        
        # Default recommendation
        return (
            "document_search",
            0.6,
            "General document search is recommended for this query type"
        )
    
    def _generate_search_suggestions(
        self, 
        query: str, 
        query_type: str, 
        key_terms: List[str]
    ) -> List[str]:
        """Generate search suggestions to improve results."""
        suggestions = []
        
        # Suggest using quotes for phrases
        if len(query.split()) > 2 and '"' not in query:
            suggestions.append(f'Try using quotes for exact phrases: "{query}"')
        
        # Suggest filters based on query content
        if any(word in query.lower() for word in ['author', 'written', 'by']):
            suggestions.append("Consider using author filters: author:name")
        
        if any(word in query.lower() for word in ['category', 'topic', 'subject']):
            suggestions.append("Consider using category filters: category:name")
        
        # Suggest alternative terms
        if query_type == "definitional":
            suggestions.append("Try searching for 'definition', 'meaning', or 'explanation'")
        
        elif query_type == "procedural":
            suggestions.append("Try including terms like 'steps', 'guide', 'tutorial', or 'how-to'")
        
        # Suggest broadening or narrowing search
        if len(key_terms) > 6:
            suggestions.append("Consider using fewer, more specific terms")
        elif len(key_terms) < 2:
            suggestions.append("Consider adding more specific terms to narrow results")
        
        return suggestions
    
    def _assess_complexity(self, query: str, key_terms: List[str]) -> str:
        """Assess the complexity of the query."""
        word_count = len(query.split())
        term_count = len(key_terms)
        
        if word_count > 15 or term_count > 8:
            return "high"
        elif word_count > 8 or term_count > 4:
            return "medium"
        else:
            return "low"


class CollectionStatsTool(BaseTool):
    """Tool for getting collection statistics and health information."""
    
    def __init__(self):
        self.solr_url = f"{settings.solr_url}/{settings.solr_collection}"
        self.solr = pysolr.Solr(self.solr_url, always_commit=True)
        
        metadata = {
            "name": "get_collection_stats",
            "description": (
                "Get comprehensive statistics about the document collection including "
                "total documents, document types, authors, categories, and health metrics. "
                "Useful for understanding the collection composition and status."
            )
        }
        super().__init__(metadata=metadata)
    
    def call(self, include_details: bool = True, **kwargs) -> str:
        """
        Get collection statistics.
        
        Args:
            include_details: Whether to include detailed breakdowns
            
        Returns:
            JSON string with collection statistics
        """
        try:
            logger.info("Getting collection statistics")
            
            # Basic stats query
            basic_results = self.solr.search(
                "*:*", 
                rows=0,
                facet="true",
                facet_field=["document_type", "is_chunk", "author", "category", "tags"],
                facet_limit=20
            )
            
            stats = {
                "total_documents": basic_results.hits,
                "collection_health": "healthy" if basic_results.hits > 0 else "empty",
                "last_updated": datetime.utcnow().isoformat(),
                "solr_url": self.solr_url
            }
            
            # Process facets
            if hasattr(basic_results, 'facets') and basic_results.facets:
                facet_fields = basic_results.facets.get('facet_fields', {})
                
                # Document types
                if 'document_type' in facet_fields:
                    doc_types = facet_fields['document_type']
                    stats["document_types"] = {
                        doc_types[i]: doc_types[i+1] 
                        for i in range(0, len(doc_types), 2)
                    }
                
                # Chunks vs full documents
                if 'is_chunk' in facet_fields:
                    chunk_data = facet_fields['is_chunk']
                    chunk_stats = {
                        chunk_data[i]: chunk_data[i+1] 
                        for i in range(0, len(chunk_data), 2)
                    }
                    stats["document_structure"] = {
                        "full_documents": chunk_stats.get("false", 0),
                        "chunks": chunk_stats.get("true", 0)
                    }
                
                if include_details:
                    # Top authors
                    if 'author' in facet_fields:
                        authors = facet_fields['author']
                        stats["top_authors"] = {
                            authors[i]: authors[i+1] 
                            for i in range(0, min(len(authors), 20), 2)
                        }
                    
                    # Top categories
                    if 'category' in facet_fields:
                        categories = facet_fields['category']
                        stats["top_categories"] = {
                            categories[i]: categories[i+1] 
                            for i in range(0, min(len(categories), 20), 2)
                        }
                    
                    # Top tags
                    if 'tags' in facet_fields:
                        tags = facet_fields['tags']
                        stats["top_tags"] = {
                            tags[i]: tags[i+1] 
                            for i in range(0, min(len(tags), 20), 2)
                        }
            
            # Get content statistics
            if include_details:
                content_stats = self._get_content_statistics()
                stats.update(content_stats)
            
            logger.info("Collection statistics retrieved", 
                       total_docs=stats["total_documents"])
            
            return json.dumps(stats, indent=2)
            
        except Exception as e:
            logger.error("Failed to get collection stats", error=str(e))
            return json.dumps({
                "error": f"Failed to get collection statistics: {str(e)}",
                "collection_health": "error"
            })
    
    def _get_content_statistics(self) -> Dict[str, Any]:
        """Get detailed content statistics."""
        try:
            # Get documents with content length info
            results = self.solr.search(
                "*:*",
                rows=1000,  # Sample for statistics
                fl="content_length,word_count,created_date",
                fq="is_chunk:false"  # Only full documents
            )
            
            if not results.docs:
                return {"content_stats": "No documents found"}
            
            content_lengths = []
            word_counts = []
            dates = []
            
            for doc in results.docs:
                if doc.get("content_length"):
                    content_lengths.append(doc["content_length"])
                if doc.get("word_count"):
                    word_counts.append(doc["word_count"])
                if doc.get("created_date"):
                    dates.append(doc["created_date"])
            
            stats = {}
            
            if content_lengths:
                stats["content_length_stats"] = {
                    "min": min(content_lengths),
                    "max": max(content_lengths),
                    "average": sum(content_lengths) // len(content_lengths),
                    "total": sum(content_lengths)
                }
            
            if word_counts:
                stats["word_count_stats"] = {
                    "min": min(word_counts),
                    "max": max(word_counts),
                    "average": sum(word_counts) // len(word_counts),
                    "total": sum(word_counts)
                }
            
            if dates:
                stats["temporal_distribution"] = {
                    "oldest_document": min(dates),
                    "newest_document": max(dates),
                    "documents_analyzed": len(dates)
                }
            
            return stats
            
        except Exception as e:
            logger.error("Failed to get content statistics", error=str(e))
            return {"content_stats_error": str(e)}
