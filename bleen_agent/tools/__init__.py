"""
Tools module for <PERSON><PERSON> Agent.

This module provides all the tools available to the agent and handles
tool registration and initialization.
"""

from typing import List
from llama_index.core.tools import BaseTool

from .search import DocumentSearchTool, FilteredSearchTool, SimilarDocumentsTool
from .document_management import DocumentImportTool, DocumentDeletionTool, DocumentDetailsTool
from .analysis import QueryAnalysisTool, CollectionStatsTool

# All available tools
ALL_TOOLS = [
    DocumentSearchTool,
    FilteredSearchTool,
    SimilarDocumentsTool,
    DocumentImportTool,
    DocumentDeletionTool,
    DocumentDetailsTool,
    QueryAnalysisTool,
    CollectionStatsTool
]


def get_all_tools() -> List[BaseTool]:
    """Get all available tools as instances."""
    return [tool_class() for tool_class in ALL_TOOLS]


def get_search_tools() -> List[BaseTool]:
    """Get only search-related tools."""
    search_tools = [
        DocumentSearchTool,
        FilteredSearchTool,
        SimilarDocumentsTool
    ]
    return [tool_class() for tool_class in search_tools]


def get_management_tools() -> List[BaseTool]:
    """Get only document management tools."""
    management_tools = [
        DocumentImportTool,
        DocumentDeletionTool,
        DocumentDetailsTool
    ]
    return [tool_class() for tool_class in management_tools]


def get_analysis_tools() -> List[BaseTool]:
    """Get only analysis tools."""
    analysis_tools = [
        QueryAnalysisTool,
        CollectionStatsTool
    ]
    return [tool_class() for tool_class in analysis_tools]


__all__ = [
    "ALL_TOOLS",
    "get_all_tools",
    "get_search_tools",
    "get_management_tools",
    "get_analysis_tools",
    "DocumentSearchTool",
    "FilteredSearchTool",
    "SimilarDocumentsTool",
    "DocumentImportTool",
    "DocumentDeletionTool",
    "DocumentDetailsTool",
    "QueryAnalysisTool",
    "CollectionStatsTool"
]
