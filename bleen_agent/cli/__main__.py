#!/usr/bin/env python3
"""
Main entry point for Bleen Agent CLI.
"""

import sys
import click
from pathlib import Path

from ..core.config import settings
from ..utils.logging import setup_logging
from .interface import B<PERSON><PERSON><PERSON>


@click.command()
@click.option('--solr-url', default=settings.solr_url, help='Solr server URL')
@click.option('--collection', default=settings.solr_collection, help='Solr collection name')
@click.option('--agent-type', default='full', help='Agent type (full, search, management, or preset name)')
@click.option('--verbose', '-v', is_flag=True, help='Enable verbose logging')
@click.option('--config-file', type=click.Path(exists=True), help='Path to configuration file')
def main(solr_url, collection, agent_type, verbose, config_file):
    """
    Bleen Agent - AI-powered document retrieval and question answering.

    Start an interactive session to ask questions about your documents.
    Built with LlamaIndex and tool-based architecture.
    """

    # Setup logging
    setup_logging()

    # Update settings if provided
    if solr_url != settings.solr_url:
        settings.solr_url = solr_url
    if collection != settings.solr_collection:
        settings.solr_collection = collection
    if verbose:
        settings.log_level = "DEBUG"
        settings.agent_verbose = True

    # Load config file if provided
    if config_file:
        # This would load additional configuration
        # For now, we'll just note it
        click.echo(f"Loading config from: {config_file}")

    try:
        # Start the CLI with specified agent type
        cli = BleenCLI(agent_type=agent_type)
        cli.start()

    except KeyboardInterrupt:
        click.echo("\nGoodbye!")
        sys.exit(0)
    except Exception as e:
        click.echo(f"Error: {str(e)}", err=True)
        sys.exit(1)


if __name__ == '__main__':
    main()
