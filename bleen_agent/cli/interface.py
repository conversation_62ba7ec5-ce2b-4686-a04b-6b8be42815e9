"""
Interactive command-line interface for <PERSON><PERSON> Agent.
"""

import sys
from typing import Dict, Any, Optional
from rich.console import Console
from rich.panel import Panel
from rich.table import Table
from rich.markdown import Markdown
from rich.prompt import Prompt, Confirm
from rich.text import Text
from rich.columns import Columns

from ..core.agent import <PERSON><PERSON><PERSON><PERSON>
from ..core.config import settings
from ..utils.logging import setup_logging, get_logger

# Setup logging
setup_logging()
logger = get_logger(__name__)


class BleenCLI:
    """
    Interactive command-line interface for Bleen Agent.
    """
    
    def __init__(self):
        self.console = Console()
        self.agent: Optional[BleenAgent] = None
        self.running = False
        
        # CLI commands
        self.commands = {
            'help': self._show_help,
            'stats': self._show_stats,
            'history': self._show_history,
            'clear': self._clear_history,
            'search': self._search_documents,
            'similar': self._find_similar,
            'settings': self._show_settings,
            'quit': self._quit,
            'exit': self._quit
        }
    
    def start(self) -> None:
        """Start the interactive CLI."""
        self._show_welcome()
        
        try:
            # Initialize agent
            self.console.print("[dim]Initializing Bleen Agent...[/dim]")
            self.agent = BleenAgent()
            self.running = True
            
            self.console.print("[green]✓[/green] Agent initialized successfully!")
            self.console.print()
            
            # Main interaction loop
            self._interaction_loop()
            
        except Exception as e:
            self.console.print(f"[bold red]Error:[/bold red] Failed to initialize agent: {str(e)}")
            sys.exit(1)
    
    def _interaction_loop(self) -> None:
        """Main interaction loop."""
        while self.running:
            try:
                # Get user input
                user_input = Prompt.ask(
                    "[bold blue]You[/bold blue]",
                    default="",
                    show_default=False
                ).strip()
                
                if not user_input:
                    continue
                
                # Check if it's a command
                if user_input.startswith('/'):
                    self._handle_command(user_input[1:])
                else:
                    # Process as a question
                    self._handle_question(user_input)
                
                self.console.print()
                
            except KeyboardInterrupt:
                self.console.print("\n[dim]Use /quit to exit[/dim]")
            except EOFError:
                break
        
        self._show_goodbye()
    
    def _handle_question(self, question: str) -> None:
        """Handle a user question."""
        self.console.print("[dim]Thinking...[/dim]")
        
        try:
            # Get answer from agent
            response = self.agent.ask(question)
            
            # Display answer
            self._display_answer(response)
            
        except Exception as e:
            self.console.print(f"[bold red]Error:[/bold red] {str(e)}")
    
    def _handle_command(self, command_line: str) -> None:
        """Handle a CLI command."""
        parts = command_line.split()
        if not parts:
            return
        
        command = parts[0].lower()
        args = parts[1:] if len(parts) > 1 else []
        
        if command in self.commands:
            try:
                self.commands[command](args)
            except Exception as e:
                self.console.print(f"[bold red]Command error:[/bold red] {str(e)}")
        else:
            self.console.print(f"[yellow]Unknown command:[/yellow] {command}")
            self.console.print("Type [bold]/help[/bold] for available commands")
    
    def _display_answer(self, response: Dict[str, Any]) -> None:
        """Display the agent's answer with formatting."""
        # Main answer
        answer_panel = Panel(
            Markdown(response["answer"]),
            title="[bold green]Bleen Agent[/bold green]",
            border_style="green"
        )
        self.console.print(answer_panel)
        
        # Confidence and metadata
        confidence = response.get("confidence", 0.0)
        confidence_color = "green" if confidence > 0.7 else "yellow" if confidence > 0.4 else "red"
        
        metadata_text = Text()
        metadata_text.append(f"Confidence: ", style="dim")
        metadata_text.append(f"{confidence:.1%}", style=confidence_color)
        metadata_text.append(f" | Processing time: ", style="dim")
        metadata_text.append(f"{response['metadata']['processing_time_seconds']:.2f}s", style="cyan")
        metadata_text.append(f" | Sources: ", style="dim")
        metadata_text.append(f"{response['metadata']['num_sources']}", style="blue")
        
        self.console.print(metadata_text)
        
        # Sources (if any)
        if response.get("sources"):
            self._display_sources(response["sources"])
        
        # Query analysis (if verbose mode or low confidence)
        if confidence < 0.5 or response.get("query_analysis"):
            self._display_query_analysis(response.get("query_analysis"))
    
    def _display_sources(self, sources: list) -> None:
        """Display source information."""
        if not sources:
            return
        
        self.console.print("\n[bold]Sources:[/bold]")
        
        for i, source in enumerate(sources[:5], 1):  # Show max 5 sources
            source_text = Text()
            source_text.append(f"{i}. ", style="dim")
            source_text.append(source.get("title", "Untitled"), style="bold")
            
            if source.get("source_file"):
                source_text.append(f" ({source['source_file']})", style="dim")
            
            source_text.append(f" [Score: {source.get('relevance_score', 0):.2f}]", style="cyan")
            
            self.console.print(source_text)
            
            # Show preview
            preview = source.get("preview", "")
            if preview:
                self.console.print(f"   [dim]{preview}[/dim]")
    
    def _display_query_analysis(self, analysis: Optional[Dict[str, Any]]) -> None:
        """Display query analysis information."""
        if not analysis:
            return
        
        self.console.print("\n[bold]Query Analysis:[/bold]")
        
        analysis_table = Table(show_header=False, box=None, padding=(0, 1))
        analysis_table.add_column("Key", style="cyan")
        analysis_table.add_column("Value", style="white")
        
        analysis_table.add_row("Query Type", analysis.get("query_type", "unknown"))
        analysis_table.add_row("Search Strategy", analysis.get("search_strategy", "unknown"))
        analysis_table.add_row("Key Terms", ", ".join(analysis.get("key_terms", [])))
        
        if analysis.get("filters"):
            filters_str = ", ".join(f"{k}:{v}" for k, v in analysis["filters"].items())
            analysis_table.add_row("Filters", filters_str)
        
        self.console.print(analysis_table)
    
    def _show_help(self, args: list) -> None:
        """Show help information."""
        help_table = Table(title="Bleen Agent Commands")
        help_table.add_column("Command", style="cyan")
        help_table.add_column("Description", style="white")
        
        help_table.add_row("/help", "Show this help message")
        help_table.add_row("/stats", "Show collection statistics")
        help_table.add_row("/history", "Show conversation history")
        help_table.add_row("/clear", "Clear conversation history")
        help_table.add_row("/search <query>", "Search documents directly")
        help_table.add_row("/similar <doc_id>", "Find similar documents")
        help_table.add_row("/settings", "Show current settings")
        help_table.add_row("/quit or /exit", "Exit the application")
        
        self.console.print(help_table)
        self.console.print("\n[dim]You can also just type your questions directly![/dim]")
    
    def _show_stats(self, args: list) -> None:
        """Show collection statistics."""
        if not self.agent:
            self.console.print("[red]Agent not initialized[/red]")
            return
        
        self.console.print("[dim]Getting collection statistics...[/dim]")
        
        try:
            stats = self.agent.get_collection_stats()
            
            stats_table = Table(title="Collection Statistics")
            stats_table.add_column("Metric", style="cyan")
            stats_table.add_column("Value", style="green")
            
            stats_table.add_row("Total Documents", str(stats.get("total_documents", 0)))
            
            # Document types
            if "document_types" in stats:
                for doc_type, count in stats["document_types"].items():
                    stats_table.add_row(f"Type: {doc_type}", str(count))
            
            # Chunks vs documents
            if "chunks_vs_documents" in stats:
                for chunk_type, count in stats["chunks_vs_documents"].items():
                    label = "Chunks" if chunk_type == "true" else "Full Documents"
                    stats_table.add_row(label, str(count))
            
            self.console.print(stats_table)
            
        except Exception as e:
            self.console.print(f"[red]Error getting stats: {str(e)}[/red]")
    
    def _show_history(self, args: list) -> None:
        """Show conversation history."""
        if not self.agent:
            self.console.print("[red]Agent not initialized[/red]")
            return
        
        history = self.agent.get_conversation_history()
        
        if not history:
            self.console.print("[dim]No conversation history[/dim]")
            return
        
        self.console.print(f"[bold]Conversation History ({len(history)} entries):[/bold]\n")
        
        for i, entry in enumerate(history[-10:], 1):  # Show last 10 entries
            # Question
            self.console.print(f"[bold blue]{i}. You:[/bold blue] {entry['question']}")
            
            # Answer (truncated)
            answer = entry['answer']
            if len(answer) > 200:
                answer = answer[:200] + "..."
            
            self.console.print(f"[bold green]   Agent:[/bold green] {answer}")
            
            # Metadata
            confidence = entry.get('confidence', 0.0)
            confidence_color = "green" if confidence > 0.7 else "yellow" if confidence > 0.4 else "red"
            
            metadata_text = Text()
            metadata_text.append("   ", style="dim")
            metadata_text.append(f"Confidence: {confidence:.1%}", style=confidence_color)
            metadata_text.append(f" | Sources: {entry.get('num_sources', 0)}", style="dim")
            
            self.console.print(metadata_text)
            self.console.print()
    
    def _clear_history(self, args: list) -> None:
        """Clear conversation history."""
        if not self.agent:
            self.console.print("[red]Agent not initialized[/red]")
            return
        
        if Confirm.ask("Are you sure you want to clear the conversation history?"):
            self.agent.clear_conversation_history()
            self.console.print("[green]✓[/green] Conversation history cleared")
    
    def _search_documents(self, args: list) -> None:
        """Search documents directly."""
        if not self.agent:
            self.console.print("[red]Agent not initialized[/red]")
            return
        
        if not args:
            query = Prompt.ask("Enter search query")
        else:
            query = " ".join(args)
        
        if not query:
            return
        
        self.console.print(f"[dim]Searching for: {query}[/dim]")
        
        try:
            results = self.agent.search_documents(query)
            
            if not results["results"]:
                self.console.print("[yellow]No results found[/yellow]")
                return
            
            self.console.print(f"[bold]Found {len(results['results'])} results:[/bold]\n")
            
            for i, result in enumerate(results["results"][:10], 1):
                self.console.print(f"[bold]{i}. {result['title']}[/bold]")
                self.console.print(f"   ID: {result['id']}")
                self.console.print(f"   Score: {result['relevance_score']:.3f}")
                self.console.print(f"   Preview: [dim]{result['content_preview']}[/dim]")
                self.console.print()
                
        except Exception as e:
            self.console.print(f"[red]Search error: {str(e)}[/red]")
    
    def _find_similar(self, args: list) -> None:
        """Find similar documents."""
        if not self.agent:
            self.console.print("[red]Agent not initialized[/red]")
            return
        
        if not args:
            doc_id = Prompt.ask("Enter document ID")
        else:
            doc_id = args[0]
        
        if not doc_id:
            return
        
        self.console.print(f"[dim]Finding documents similar to: {doc_id}[/dim]")
        
        try:
            results = self.agent.get_similar_documents(doc_id)
            
            similar_docs = results.get("similar_documents", [])
            
            if not similar_docs:
                self.console.print("[yellow]No similar documents found[/yellow]")
                return
            
            self.console.print(f"[bold]Found {len(similar_docs)} similar documents:[/bold]\n")
            
            for i, doc in enumerate(similar_docs, 1):
                self.console.print(f"[bold]{i}. {doc['title']}[/bold]")
                self.console.print(f"   ID: {doc['id']}")
                self.console.print(f"   Similarity: {doc['relevance_score']:.3f}")
                self.console.print(f"   Preview: [dim]{doc['content_preview']}[/dim]")
                self.console.print()
                
        except Exception as e:
            self.console.print(f"[red]Error finding similar documents: {str(e)}[/red]")
    
    def _show_settings(self, args: list) -> None:
        """Show current settings."""
        settings_table = Table(title="Current Settings")
        settings_table.add_column("Setting", style="cyan")
        settings_table.add_column("Value", style="white")
        
        settings_table.add_row("Solr URL", settings.solr_url)
        settings_table.add_row("Collection", settings.solr_collection)
        settings_table.add_row("LLM Model", settings.llm_model)
        settings_table.add_row("Max Results", str(settings.max_search_results))
        settings_table.add_row("Chunk Size", str(settings.max_chunk_size))
        settings_table.add_row("Log Level", settings.log_level)
        
        self.console.print(settings_table)
    
    def _quit(self, args: list) -> None:
        """Quit the application."""
        self.running = False
    
    def _show_welcome(self) -> None:
        """Show welcome message."""
        welcome_text = """
[bold blue]Welcome to Bleen Agent![/bold blue]

An AI-powered document retrieval and question answering system.

[dim]Commands start with / (e.g., /help)
Just type your questions directly to get answers![/dim]
        """
        
        welcome_panel = Panel(
            welcome_text.strip(),
            title="[bold]Bleen Agent v0.1.0[/bold]",
            border_style="blue"
        )
        
        self.console.print(welcome_panel)
        self.console.print()
    
    def _show_goodbye(self) -> None:
        """Show goodbye message."""
        self.console.print("\n[bold blue]Thank you for using Bleen Agent![/bold blue]")
        self.console.print("[dim]Goodbye![/dim]")
