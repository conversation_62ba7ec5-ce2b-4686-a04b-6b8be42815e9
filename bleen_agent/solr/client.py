"""
Solr client wrapper for <PERSON><PERSON> Agent with enhanced search and indexing capabilities.
"""

import json
import uuid
from typing import Dict, List, Optional, Any, Union
from datetime import datetime, timezone

import pysolr
from openai import OpenAI

from ..core.config import settings
from ..models.document import Document, DocumentChunk, SearchResult, QueryContext
from ..utils.logging import get_logger

logger = get_logger(__name__)


class SolrClient:
    """
    Enhanced Solr client with document indexing and advanced search capabilities.
    """
    
    def __init__(self, solr_url: str = None, collection: str = None):
        self.solr_url = solr_url or settings.solr_url
        self.collection = collection or settings.solr_collection
        self.full_url = f"{self.solr_url}/{self.collection}"
        
        # Initialize Solr connection
        self.solr = pysolr.Solr(self.full_url, always_commit=True)
        
        # Initialize OpenAI client for embeddings
        self.openai_client = OpenAI(api_key=settings.openai_api_key)
        
        logger.info("Initialized Solr client", url=self.full_url)
    
    def ping(self) -> bool:
        """Check if Solr is accessible."""
        try:
            self.solr.ping()
            return True
        except Exception as e:
            logger.error("Solr ping failed", error=str(e))
            return False
    
    def index_document(self, document: Document, generate_embedding: bool = True) -> bool:
        """
        Index a complete document in Solr.
        
        Args:
            document: Document to index
            generate_embedding: Whether to generate embedding vector
            
        Returns:
            True if successful
        """
        try:
            # Generate embedding if requested
            if generate_embedding and not document.content_vector:
                document.content_vector = self._generate_embedding(document.content)
            
            # Convert to Solr document format
            solr_doc = self._document_to_solr(document)
            
            # Index in Solr
            self.solr.add([solr_doc])
            
            logger.info("Indexed document", document_id=document.document_id)
            return True
            
        except Exception as e:
            logger.error("Failed to index document", 
                        document_id=document.document_id, 
                        error=str(e))
            return False
    
    def index_chunk(self, chunk: DocumentChunk, generate_embedding: bool = True) -> bool:
        """
        Index a document chunk in Solr.
        
        Args:
            chunk: Document chunk to index
            generate_embedding: Whether to generate embedding vector
            
        Returns:
            True if successful
        """
        try:
            # Generate embedding if requested
            if generate_embedding and not chunk.content_vector:
                chunk.content_vector = self._generate_embedding(chunk.content)
            
            # Convert to Solr document format
            solr_doc = self._chunk_to_solr(chunk)
            
            # Index in Solr
            self.solr.add([solr_doc])
            
            logger.info("Indexed chunk", chunk_id=chunk.chunk_id)
            return True
            
        except Exception as e:
            logger.error("Failed to index chunk", 
                        chunk_id=chunk.chunk_id, 
                        error=str(e))
            return False
    
    def search_fulltext(
        self, 
        query: str, 
        filters: Dict[str, Any] = None,
        max_results: int = None
    ) -> List[SearchResult]:
        """
        Perform full-text search using Solr's text search capabilities.
        
        Args:
            query: Search query
            filters: Additional filters to apply
            max_results: Maximum number of results
            
        Returns:
            List of search results
        """
        max_results = max_results or settings.max_search_results
        
        try:
            # Build Solr query
            solr_query = self._build_fulltext_query(query, filters)
            
            # Execute search
            results = self.solr.search(
                solr_query,
                rows=max_results,
                fl="*,score",
                sort="score desc"
            )
            
            # Convert to SearchResult objects
            search_results = []
            for doc in results:
                search_result = self._solr_to_search_result(doc, "fulltext")
                search_results.append(search_result)
            
            logger.info("Full-text search completed", 
                       query=query, 
                       num_results=len(search_results))
            
            return search_results
            
        except Exception as e:
            logger.error("Full-text search failed", query=query, error=str(e))
            return []
    
    def search_vector(
        self, 
        query: str, 
        filters: Dict[str, Any] = None,
        max_results: int = None
    ) -> List[SearchResult]:
        """
        Perform vector similarity search using embeddings.
        
        Args:
            query: Search query
            filters: Additional filters to apply
            max_results: Maximum number of results
            
        Returns:
            List of search results
        """
        max_results = max_results or settings.max_search_results
        
        try:
            # Generate query embedding
            query_vector = self._generate_embedding(query)
            
            # Build vector search query
            vector_query = f"{{!knn f=content_vector topK={max_results}}}{query_vector}"
            
            # Add filters if provided
            filter_queries = self._build_filter_queries(filters) if filters else []
            
            # Execute search
            results = self.solr.search(
                vector_query,
                fq=filter_queries,
                rows=max_results,
                fl="*,score"
            )
            
            # Convert to SearchResult objects
            search_results = []
            for doc in results:
                search_result = self._solr_to_search_result(doc, "vector")
                search_results.append(search_result)
            
            logger.info("Vector search completed", 
                       query=query, 
                       num_results=len(search_results))
            
            return search_results
            
        except Exception as e:
            logger.error("Vector search failed", query=query, error=str(e))
            return []
    
    def search_hybrid(
        self, 
        query: str, 
        filters: Dict[str, Any] = None,
        max_results: int = None,
        fulltext_weight: float = 0.7,
        vector_weight: float = 0.3
    ) -> List[SearchResult]:
        """
        Perform hybrid search combining full-text and vector search.
        
        Args:
            query: Search query
            filters: Additional filters to apply
            max_results: Maximum number of results
            fulltext_weight: Weight for full-text search results
            vector_weight: Weight for vector search results
            
        Returns:
            List of search results
        """
        max_results = max_results or settings.max_search_results
        
        # Get results from both search methods
        fulltext_results = self.search_fulltext(query, filters, max_results * 2)
        vector_results = self.search_vector(query, filters, max_results * 2)
        
        # Combine and re-rank results
        combined_results = self._combine_search_results(
            fulltext_results, 
            vector_results,
            fulltext_weight,
            vector_weight
        )
        
        # Return top results
        return combined_results[:max_results]
    
    def get_document(self, document_id: str) -> Optional[Dict[str, Any]]:
        """Get a document by ID."""
        try:
            results = self.solr.search(f"document_id:{document_id}", rows=1)
            return results.docs[0] if results.docs else None
        except Exception as e:
            logger.error("Failed to get document", document_id=document_id, error=str(e))
            return None
    
    def delete_document(self, document_id: str) -> bool:
        """Delete a document and all its chunks."""
        try:
            self.solr.delete(q=f"document_id:{document_id}")
            logger.info("Deleted document", document_id=document_id)
            return True
        except Exception as e:
            logger.error("Failed to delete document", document_id=document_id, error=str(e))
            return False
    
    def get_collection_stats(self) -> Dict[str, Any]:
        """Get collection statistics."""
        try:
            results = self.solr.search("*:*", rows=0, facet="true", facet_field=["document_type", "is_chunk"])
            
            stats = {
                "total_documents": results.hits,
                "document_types": {},
                "chunks_vs_documents": {}
            }
            
            # Process facets if available
            if hasattr(results, 'facets') and results.facets:
                facet_fields = results.facets.get('facet_fields', {})
                
                if 'document_type' in facet_fields:
                    doc_types = facet_fields['document_type']
                    for i in range(0, len(doc_types), 2):
                        stats["document_types"][doc_types[i]] = doc_types[i+1]
                
                if 'is_chunk' in facet_fields:
                    chunk_data = facet_fields['is_chunk']
                    for i in range(0, len(chunk_data), 2):
                        stats["chunks_vs_documents"][chunk_data[i]] = chunk_data[i+1]
            
            return stats
            
        except Exception as e:
            logger.error("Failed to get collection stats", error=str(e))
            return {"error": str(e)}
    
    def _generate_embedding(self, text: str) -> List[float]:
        """Generate embedding vector for text."""
        try:
            response = self.openai_client.embeddings.create(
                model=settings.embedding_model,
                input=text
            )
            return response.data[0].embedding
        except Exception as e:
            logger.error("Failed to generate embedding", error=str(e))
            return []
    
    def _document_to_solr(self, document: Document) -> Dict[str, Any]:
        """Convert Document to Solr document format."""
        solr_doc = {
            "id": document.document_id,
            "document_id": document.document_id,
            "is_chunk": False,
            "content": document.content,
            "content_length": document.content_length,
            "word_count": document.word_count,
            "indexed_date": datetime.now(timezone.utc).isoformat()
        }
        
        # Add optional fields
        if document.title:
            solr_doc["title"] = document.title
        if document.summary:
            solr_doc["summary"] = document.summary
        if document.author:
            solr_doc["author"] = document.author
        if document.category:
            solr_doc["category"] = document.category
        if document.tags:
            solr_doc["tags"] = document.tags
        if document.keywords:
            solr_doc["keywords"] = document.keywords
        if document.language:
            solr_doc["language"] = document.language
        if document.document_type:
            solr_doc["document_type"] = document.document_type.value
        if document.source_file:
            solr_doc["source_file"] = document.source_file
        if document.file_path:
            solr_doc["file_path"] = document.file_path
        if document.created_date:
            solr_doc["created_date"] = document.created_date.isoformat() + "Z"
        if document.modified_date:
            solr_doc["modified_date"] = document.modified_date.isoformat() + "Z"
        if document.readability_score:
            solr_doc["readability_score"] = document.readability_score
        if document.content_vector:
            solr_doc["content_vector"] = document.content_vector
        
        # Add custom fields
        for key, value in document.custom_fields.items():
            solr_doc[f"custom_{key}"] = value
        
        return solr_doc

    def _chunk_to_solr(self, chunk: DocumentChunk) -> Dict[str, Any]:
        """Convert DocumentChunk to Solr document format."""
        solr_doc = {
            "id": chunk.chunk_id,
            "document_id": chunk.document_id,
            "chunk_id": chunk.chunk_id,
            "chunk_index": chunk.chunk_index,
            "is_chunk": True,
            "content": chunk.content,
            "content_length": chunk.content_length,
            "word_count": chunk.word_count,
            "start_char": chunk.start_char,
            "end_char": chunk.end_char,
            "indexed_date": datetime.now(timezone.utc).isoformat()
        }

        # Add optional fields
        if chunk.title:
            solr_doc["title"] = chunk.title
        if chunk.author:
            solr_doc["author"] = chunk.author
        if chunk.category:
            solr_doc["category"] = chunk.category
        if chunk.tags:
            solr_doc["tags"] = chunk.tags
        if chunk.language:
            solr_doc["language"] = chunk.language
        if chunk.document_type:
            solr_doc["document_type"] = chunk.document_type.value
        if chunk.source_file:
            solr_doc["source_file"] = chunk.source_file
        if chunk.file_path:
            solr_doc["file_path"] = chunk.file_path
        if chunk.content_vector:
            solr_doc["content_vector"] = chunk.content_vector
        if chunk.previous_chunk_id:
            solr_doc["previous_chunk_id"] = chunk.previous_chunk_id
        if chunk.next_chunk_id:
            solr_doc["next_chunk_id"] = chunk.next_chunk_id

        return solr_doc

    def _build_fulltext_query(self, query: str, filters: Dict[str, Any] = None) -> str:
        """Build Solr query for full-text search."""
        # Escape special characters in query
        escaped_query = pysolr.Solr._from_python(query)

        # Build main query
        main_query = f"content:({escaped_query}) OR title:({escaped_query})^2"

        # Add filters
        if filters:
            filter_parts = []
            for field, value in filters.items():
                if isinstance(value, list):
                    filter_parts.append(f"{field}:({' OR '.join(map(str, value))})")
                else:
                    filter_parts.append(f"{field}:{value}")

            if filter_parts:
                main_query += " AND " + " AND ".join(filter_parts)

        return main_query

    def _build_filter_queries(self, filters: Dict[str, Any]) -> List[str]:
        """Build filter queries for Solr."""
        filter_queries = []

        for field, value in filters.items():
            if isinstance(value, list):
                filter_queries.append(f"{field}:({' OR '.join(map(str, value))})")
            else:
                filter_queries.append(f"{field}:{value}")

        return filter_queries

    def _solr_to_search_result(self, solr_doc: Dict[str, Any], search_type: str) -> SearchResult:
        """Convert Solr document to SearchResult."""
        return SearchResult(
            id=solr_doc.get("id", ""),
            document_id=solr_doc.get("document_id", ""),
            is_chunk=solr_doc.get("is_chunk", False),
            content=solr_doc.get("content", ""),
            title=solr_doc.get("title"),
            score=solr_doc.get("score", 0.0),
            search_type=search_type,
            metadata={
                "author": solr_doc.get("author"),
                "category": solr_doc.get("category"),
                "tags": solr_doc.get("tags"),
                "document_type": solr_doc.get("document_type"),
                "source_file": solr_doc.get("source_file"),
                "chunk_index": solr_doc.get("chunk_index"),
                "word_count": solr_doc.get("word_count"),
                "indexed_date": solr_doc.get("indexed_date")
            }
        )

    def _combine_search_results(
        self,
        fulltext_results: List[SearchResult],
        vector_results: List[SearchResult],
        fulltext_weight: float,
        vector_weight: float
    ) -> List[SearchResult]:
        """Combine and re-rank search results from different methods."""
        # Create a map to combine results by document ID
        combined_map = {}

        # Add fulltext results
        for result in fulltext_results:
            key = result.id
            result.score = result.score * fulltext_weight
            result.search_type = "hybrid_fulltext"
            combined_map[key] = result

        # Add vector results, combining scores if document already exists
        for result in vector_results:
            key = result.id
            result.score = result.score * vector_weight

            if key in combined_map:
                # Combine scores
                combined_map[key].score += result.score
                combined_map[key].search_type = "hybrid_combined"
            else:
                result.search_type = "hybrid_vector"
                combined_map[key] = result

        # Sort by combined score
        combined_results = list(combined_map.values())
        combined_results.sort(key=lambda x: x.score, reverse=True)

        return combined_results
