"""
<PERSON>leen Agent - AI Agent for Document Retrieval and Question Answering

A comprehensive AI agent system built with LlamaIndex that uses Apache Solr
for document storage and provides intelligent document search and analysis
capabilities through a tool-based architecture.

Key Features:
- Tool-based agent architecture following best practices
- Full-text search without embeddings for better transparency
- Intelligent document management and analysis
- Conversation memory with automatic summarization
- Comprehensive logging and reasoning transparency
"""

__version__ = "0.2.0"
__author__ = "Bleen Agent Team"
__email__ = "<EMAIL>"

# Core agent and factory
from .agent.core import BleenAgent
from .agent.factory import (
    AgentFactory,
    create_agent,
    create_agent_with_health_check,
    create_preset_agent,
    AGENT_PRESETS
)

# Configuration
from .core.config import Settings, settings

# Tools (for advanced usage)
from .tools import (
    get_all_tools,
    get_search_tools,
    get_management_tools,
    get_analysis_tools
)

# Memory system
from .memory.conversation import ConversationMemory

__all__ = [
    # Core classes
    "BleenAgent",
    "AgentFactory",
    "ConversationMemory",
    "Settings",
    "settings",

    # Factory functions
    "create_agent",
    "create_agent_with_health_check",
    "create_preset_agent",

    # Tool functions
    "get_all_tools",
    "get_search_tools",
    "get_management_tools",
    "get_analysis_tools",

    # Presets
    "AGENT_PRESETS"
]
