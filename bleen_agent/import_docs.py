#!/usr/bin/env python3
"""
Command-line script for importing documents into Bleen Agent.
"""

import sys
import click
from pathlib import Path
from rich.console import Console
from rich.table import Table
from rich.progress import Progress, SpinnerColumn, TextColumn

from .core.config import settings
from .import_system.importer import DocumentImporter
from .solr.client import SolrClient
from .utils.logging import setup_logging, get_logger

# Setup logging
setup_logging()
logger = get_logger(__name__)
console = Console()


@click.group()
@click.option('--solr-url', default=settings.solr_url, help='Solr server URL')
@click.option('--collection', default=settings.solr_collection, help='Solr collection name')
@click.option('--verbose', '-v', is_flag=True, help='Enable verbose logging')
@click.pass_context
def cli(ctx, solr_url, collection, verbose):
    """Bleen Agent Document Import Tool"""
    ctx.ensure_object(dict)
    ctx.obj['solr_url'] = solr_url
    ctx.obj['collection'] = collection
    ctx.obj['verbose'] = verbose
    
    if verbose:
        console.print(f"[dim]Solr URL: {solr_url}[/dim]")
        console.print(f"[dim]Collection: {collection}[/dim]")


@cli.command()
@click.argument('file_path', type=click.Path(exists=True))
@click.option('--no-chunk', is_flag=True, help='Disable document chunking')
@click.pass_context
def import_file(ctx, file_path, no_chunk):
    """Import a single file."""
    console.print(f"[bold blue]Importing file:[/bold blue] {file_path}")
    
    try:
        # Initialize importer
        solr_client = SolrClient(ctx.obj['solr_url'], ctx.obj['collection'])
        importer = DocumentImporter(solr_client)
        
        # Check Solr connection
        if not solr_client.ping():
            console.print("[bold red]Error:[/bold red] Cannot connect to Solr")
            sys.exit(1)
        
        # Import file
        with Progress(
            SpinnerColumn(),
            TextColumn("[progress.description]{task.description}"),
            console=console
        ) as progress:
            task = progress.add_task("Importing file...", total=None)
            
            success = importer.import_file(file_path, chunk_documents=not no_chunk)
            
            progress.update(task, completed=True)
        
        if success:
            console.print("[bold green]✓[/bold green] File imported successfully")
        else:
            console.print("[bold red]✗[/bold red] File import failed")
            sys.exit(1)
            
    except Exception as e:
        console.print(f"[bold red]Error:[/bold red] {str(e)}")
        sys.exit(1)


@cli.command()
@click.argument('directory', type=click.Path(exists=True, file_okay=False, dir_okay=True))
@click.option('--recursive/--no-recursive', default=True, help='Scan subdirectories')
@click.option('--no-chunk', is_flag=True, help='Disable document chunking')
@click.option('--batch-size', default=10, help='Batch size for processing')
@click.pass_context
def import_directory(ctx, directory, recursive, no_chunk, batch_size):
    """Import all documents from a directory."""
    console.print(f"[bold blue]Importing directory:[/bold blue] {directory}")
    console.print(f"[dim]Recursive: {recursive}, Chunking: {not no_chunk}, Batch size: {batch_size}[/dim]")
    
    try:
        # Initialize importer
        solr_client = SolrClient(ctx.obj['solr_url'], ctx.obj['collection'])
        importer = DocumentImporter(solr_client)
        
        # Check Solr connection
        if not solr_client.ping():
            console.print("[bold red]Error:[/bold red] Cannot connect to Solr")
            sys.exit(1)
        
        # Import directory
        with Progress(
            SpinnerColumn(),
            TextColumn("[progress.description]{task.description}"),
            console=console
        ) as progress:
            task = progress.add_task("Importing directory...", total=None)
            
            stats = importer.import_directory(
                directory=directory,
                recursive=recursive,
                chunk_documents=not no_chunk,
                batch_size=batch_size
            )
            
            progress.update(task, completed=True)
        
        # Display results
        _display_import_stats(stats)
        
        if stats['failed'] > 0:
            sys.exit(1)
            
    except Exception as e:
        console.print(f"[bold red]Error:[/bold red] {str(e)}")
        sys.exit(1)


@cli.command()
@click.argument('document_id')
@click.pass_context
def delete(ctx, document_id):
    """Delete a document by ID."""
    console.print(f"[bold red]Deleting document:[/bold red] {document_id}")
    
    try:
        # Initialize importer
        solr_client = SolrClient(ctx.obj['solr_url'], ctx.obj['collection'])
        importer = DocumentImporter(solr_client)
        
        # Check Solr connection
        if not solr_client.ping():
            console.print("[bold red]Error:[/bold red] Cannot connect to Solr")
            sys.exit(1)
        
        # Confirm deletion
        if not click.confirm("Are you sure you want to delete this document?"):
            console.print("Deletion cancelled")
            return
        
        # Delete document
        success = importer.delete_document(document_id)
        
        if success:
            console.print("[bold green]✓[/bold green] Document deleted successfully")
        else:
            console.print("[bold red]✗[/bold red] Document deletion failed")
            sys.exit(1)
            
    except Exception as e:
        console.print(f"[bold red]Error:[/bold red] {str(e)}")
        sys.exit(1)


@cli.command()
@click.pass_context
def status(ctx):
    """Show collection status and statistics."""
    try:
        # Initialize importer
        solr_client = SolrClient(ctx.obj['solr_url'], ctx.obj['collection'])
        importer = DocumentImporter(solr_client)
        
        # Check Solr connection
        if not solr_client.ping():
            console.print("[bold red]Error:[/bold red] Cannot connect to Solr")
            sys.exit(1)
        
        # Get status
        stats = importer.get_import_status()
        
        # Display status
        _display_collection_status(stats)
        
    except Exception as e:
        console.print(f"[bold red]Error:[/bold red] {str(e)}")
        sys.exit(1)


@cli.command()
@click.pass_context
def test_connection(ctx):
    """Test connection to Solr."""
    console.print("Testing Solr connection...")
    
    try:
        solr_client = SolrClient(ctx.obj['solr_url'], ctx.obj['collection'])
        
        if solr_client.ping():
            console.print("[bold green]✓[/bold green] Solr connection successful")
            console.print(f"[dim]URL: {solr_client.full_url}[/dim]")
        else:
            console.print("[bold red]✗[/bold red] Solr connection failed")
            sys.exit(1)
            
    except Exception as e:
        console.print(f"[bold red]Error:[/bold red] {str(e)}")
        sys.exit(1)


def _display_import_stats(stats):
    """Display import statistics in a table."""
    table = Table(title="Import Results")
    table.add_column("Metric", style="cyan")
    table.add_column("Value", style="green")
    
    table.add_row("Total Files", str(stats['total_files']))
    table.add_row("Successful", str(stats['successful']))
    table.add_row("Failed", str(stats['failed']))
    table.add_row("Chunks Created", str(stats['chunks_created']))
    
    success_rate = (stats['successful'] / stats['total_files'] * 100) if stats['total_files'] > 0 else 0
    table.add_row("Success Rate", f"{success_rate:.1f}%")
    
    console.print(table)


def _display_collection_status(stats):
    """Display collection status in a table."""
    table = Table(title="Collection Status")
    table.add_column("Metric", style="cyan")
    table.add_column("Value", style="green")
    
    table.add_row("Total Documents", str(stats.get('total_documents', 0)))
    
    # Document types
    if 'document_types' in stats:
        for doc_type, count in stats['document_types'].items():
            table.add_row(f"Type: {doc_type}", str(count))
    
    # Chunks vs documents
    if 'chunks_vs_documents' in stats:
        for chunk_type, count in stats['chunks_vs_documents'].items():
            label = "Chunks" if chunk_type == "true" else "Full Documents"
            table.add_row(label, str(count))
    
    console.print(table)


def main():
    """Main entry point."""
    cli()


if __name__ == '__main__':
    main()
