"""
Conversation memory management for <PERSON>leen Agent.

Implements sophisticated memory management following best practices:
- Token-aware memory with automatic summarization
- Context preservation for multi-turn conversations
- Efficient storage and retrieval of conversation history
"""

from typing import Dict, List, Optional, Any
from datetime import datetime
import json

from llama_index.llms.openai import OpenAI

from ..core.config import settings
from ..utils.logging import get_logger

logger = get_logger(__name__)


class ConversationMemory:
    """
    Manages conversation memory with automatic summarization and context preservation.
    
    Features:
    - Token-aware memory management
    - Automatic summarization when approaching limits
    - Context preservation for related topics
    - Efficient retrieval of relevant conversation history
    """
    
    def __init__(self, max_tokens: int = None, llm: OpenAI = None):
        """
        Initialize conversation memory.
        
        Args:
            max_tokens: Maximum tokens to keep in memory
            llm: LLM instance for summarization
        """
        self.max_tokens = max_tokens or settings.memory_max_tokens
        self.llm = llm or OpenAI(
            model=settings.memory_summary_model,
            api_key=settings.openai_api_key
        )
        
        # Memory storage
        self.interactions: List[Dict[str, Any]] = []
        self.context_summary: str = ""
        self.current_tokens: int = 0
        
        # Memory metadata
        self.created_at = datetime.utcnow()
        self.last_summarized_at: Optional[datetime] = None
        
        logger.info("Conversation memory initialized", 
                   max_tokens=self.max_tokens,
                   summary_model=settings.memory_summary_model)
    
    def add_interaction(self, question: str, response: Dict[str, Any]) -> None:
        """
        Add a new interaction to memory.
        
        Args:
            question: User's question
            response: Agent's response with metadata
        """
        interaction = {
            "timestamp": datetime.utcnow().isoformat(),
            "question": question,
            "answer": response.get("answer", ""),
            "sources": response.get("sources", []),
            "confidence": response.get("confidence", 0.0),
            "reasoning_steps": response.get("reasoning_steps", []),
            "tools_used": response.get("metadata", {}).get("tools_used", [])
        }
        
        self.interactions.append(interaction)
        
        # Estimate token usage
        interaction_tokens = self._estimate_tokens(interaction)
        self.current_tokens += interaction_tokens
        
        logger.debug("Interaction added to memory",
                    tokens_added=interaction_tokens,
                    total_tokens=self.current_tokens,
                    total_interactions=len(self.interactions))
        
        # Check if we need to summarize
        if self.current_tokens > self.max_tokens:
            self._summarize_old_interactions()
    
    def add_context(self, context: Dict[str, Any]) -> None:
        """
        Add additional context to the current conversation.
        
        Args:
            context: Context information to add
        """
        if self.interactions:
            # Add context to the most recent interaction
            self.interactions[-1]["additional_context"] = context
        else:
            # Store as initial context
            self.context_summary = f"Initial context: {json.dumps(context, indent=2)}"
        
        logger.debug("Context added to memory", context_keys=list(context.keys()))
    
    def get_history(self, max_interactions: int = None) -> List[Dict[str, Any]]:
        """
        Get conversation history.
        
        Args:
            max_interactions: Maximum number of interactions to return
            
        Returns:
            List of interactions
        """
        if max_interactions:
            return self.interactions[-max_interactions:]
        return self.interactions.copy()
    
    def get_relevant_context(self, query: str, max_interactions: int = 5) -> str:
        """
        Get relevant context for a query.
        
        Args:
            query: Current query to find relevant context for
            max_interactions: Maximum interactions to consider
            
        Returns:
            Formatted context string
        """
        # Simple relevance scoring based on keyword overlap
        relevant_interactions = []
        query_words = set(query.lower().split())
        
        for interaction in self.interactions[-max_interactions:]:
            question_words = set(interaction["question"].lower().split())
            answer_words = set(interaction["answer"].lower().split())
            
            # Calculate relevance score
            question_overlap = len(query_words & question_words)
            answer_overlap = len(query_words & answer_words)
            relevance_score = question_overlap * 2 + answer_overlap
            
            if relevance_score > 0:
                relevant_interactions.append((interaction, relevance_score))
        
        # Sort by relevance and format
        relevant_interactions.sort(key=lambda x: x[1], reverse=True)
        
        context_parts = []
        if self.context_summary:
            context_parts.append(f"Previous context: {self.context_summary}")
        
        for interaction, score in relevant_interactions[:3]:  # Top 3 relevant
            context_parts.append(
                f"Previous Q: {interaction['question']}\n"
                f"Previous A: {interaction['answer'][:200]}..."
            )
        
        return "\n\n".join(context_parts)
    
    def clear(self) -> None:
        """Clear all memory."""
        self.interactions.clear()
        self.context_summary = ""
        self.current_tokens = 0
        self.last_summarized_at = None
        
        logger.info("Conversation memory cleared")
    
    def get_usage_stats(self) -> Dict[str, Any]:
        """Get memory usage statistics."""
        return {
            "total_interactions": len(self.interactions),
            "current_tokens": self.current_tokens,
            "max_tokens": self.max_tokens,
            "memory_utilization": self.current_tokens / self.max_tokens,
            "created_at": self.created_at.isoformat(),
            "last_summarized_at": self.last_summarized_at.isoformat() if self.last_summarized_at else None,
            "has_summary": bool(self.context_summary)
        }
    
    def _summarize_old_interactions(self) -> None:
        """
        Summarize old interactions to free up memory.
        
        This follows the approach recommended in the OpenAI guide:
        - Keep recent interactions intact
        - Summarize older interactions into context
        - Preserve important information
        """
        if len(self.interactions) < 3:
            return  # Not enough interactions to summarize
        
        logger.info("Starting memory summarization",
                   current_tokens=self.current_tokens,
                   interactions_count=len(self.interactions))
        
        # Keep the last 2 interactions intact
        interactions_to_keep = self.interactions[-2:]
        interactions_to_summarize = self.interactions[:-2]
        
        if not interactions_to_summarize:
            return
        
        # Create summary prompt
        summary_prompt = self._create_summary_prompt(interactions_to_summarize)
        
        try:
            # Generate summary
            response = self.llm.complete(summary_prompt)
            new_summary = str(response).strip()
            
            # Update memory
            if self.context_summary:
                self.context_summary = f"{self.context_summary}\n\n{new_summary}"
            else:
                self.context_summary = new_summary
            
            # Keep only recent interactions
            self.interactions = interactions_to_keep
            
            # Recalculate token usage
            self.current_tokens = sum(self._estimate_tokens(interaction) 
                                    for interaction in self.interactions)
            self.current_tokens += self._estimate_tokens({"text": self.context_summary})
            
            self.last_summarized_at = datetime.utcnow()
            
            logger.info("Memory summarization complete",
                       new_token_count=self.current_tokens,
                       interactions_kept=len(self.interactions),
                       summary_length=len(new_summary))
            
        except Exception as e:
            logger.error("Memory summarization failed", error=str(e))
            # Fallback: just remove oldest interactions
            self.interactions = self.interactions[-3:]
            self.current_tokens = sum(self._estimate_tokens(interaction) 
                                    for interaction in self.interactions)
    
    def _create_summary_prompt(self, interactions: List[Dict[str, Any]]) -> str:
        """Create a prompt for summarizing interactions."""
        interactions_text = []
        
        for i, interaction in enumerate(interactions, 1):
            interactions_text.append(
                f"Interaction {i}:\n"
                f"Question: {interaction['question']}\n"
                f"Answer: {interaction['answer']}\n"
                f"Tools used: {', '.join(interaction.get('tools_used', []))}\n"
                f"Confidence: {interaction.get('confidence', 0.0):.2f}"
            )
        
        return f"""Please summarize the following conversation interactions, preserving key information, topics discussed, and important findings:

{chr(10).join(interactions_text)}

Create a concise summary that captures:
1. Main topics and questions discussed
2. Key findings and answers provided
3. Important sources or documents referenced
4. Any ongoing themes or context

Summary:"""
    
    def _estimate_tokens(self, data: Any) -> int:
        """
        Estimate token count for data.
        
        Simple estimation: ~4 characters per token
        """
        if isinstance(data, dict):
            text = json.dumps(data, ensure_ascii=False)
        elif isinstance(data, str):
            text = data
        else:
            text = str(data)
        
        return len(text) // 4
