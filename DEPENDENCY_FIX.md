# 🔧 Risoluzione Conflitti Dipendenze

Se incontri errori di conflitto delle dipendenze durante l'installazione, segui questa guida.

## 🚨 Errore Comune

```
ERROR: Cannot install -r requirements.txt (line 2) and llama-index-agent-openai==0.2.0 because these package versions have conflicting dependencies.
```

## ✅ Soluzioni Rapide

### Opzione 1: Script Automatico (Raccomandato)
```bash
# Risolve automaticamente tutti i conflitti
make fix-deps
```

### Opzione 2: Installazione Manuale
```bash
# 1. Disinstalla pacchetti conflittuali
pip uninstall -y llama-index llama-index-agent-openai llama-index-llms-openai openai

# 2. Pulisci cache pip
pip cache purge

# 3. Installa dipendenze aggiornate
pip install -r requirements.txt

# 4. Installa Bleen Agent
pip install -e .
```

### Opzione 3: Installazione Minimale
```bash
# Per installazione più veloce durante sviluppo
pip install -r requirements-minimal.txt
pip install -e .
```

### Opzione 4: Ambiente Virtuale Pulito
```bash
# Crea nuovo ambiente virtuale
python -m venv venv-bleen
source venv-bleen/bin/activate  # Linux/Mac
# oppure
venv-bleen\Scripts\activate     # Windows

# Installa dipendenze
pip install --upgrade pip
pip install -r requirements.txt
pip install -e .
```

## 🔍 Verifica Installazione

### Test Rapido
```bash
# Verifica import base
python -c "import llama_index, openai; print('✅ Core dependencies OK')"

# Verifica Bleen Agent
python -c "import bleen_agent; print('✅ Bleen Agent OK')"

# Test CLI
python -m bleen_agent.cli --help
```

### Test Completo
```bash
# Avvia Solr
make solr-start-bg

# Test agente
python -m bleen_agent.cli
```

## 📋 Versioni Compatibili

Le versioni nel `requirements.txt` sono state testate e sono compatibili:

```
llama-index==0.10.57
llama-index-agent-openai==0.2.9
llama-index-llms-openai==0.1.29
openai==1.35.14
```

## 🛠️ Troubleshooting Avanzato

### Problema: Versioni Specifiche
Se hai bisogno di versioni specifiche per altri progetti:

```bash
# Usa ambiente virtuale dedicato
python -m venv venv-bleen-specific
source venv-bleen-specific/bin/activate

# Installa versioni specifiche
pip install llama-index==0.10.57
pip install llama-index-agent-openai==0.2.9
pip install -r requirements.txt
```

### Problema: Cache Corrotta
```bash
# Pulisci completamente pip
pip cache purge
pip install --no-cache-dir -r requirements.txt
```

### Problema: Conflitti Sistema
```bash
# Usa --user per installazione utente
pip install --user -r requirements.txt
pip install --user -e .
```

### Problema: Dipendenze Mancanti
```bash
# Installa dipendenze sistema (Ubuntu/Debian)
sudo apt-get update
sudo apt-get install python3-dev build-essential

# Installa dipendenze sistema (macOS)
xcode-select --install
brew install python@3.11
```

## 🐳 Alternativa Docker

Se i problemi persistono, usa Docker:

```bash
# Avvia tutto con Docker (evita conflitti locali)
make start

# Oppure solo Solr in Docker, agente locale
make solr-start
# Poi risolvi dipendenze locali
```

## 📞 Supporto

Se nessuna soluzione funziona:

1. **Crea issue** con:
   - Output completo dell'errore
   - Versione Python (`python --version`)
   - Sistema operativo
   - Output di `pip list | grep llama`

2. **Informazioni utili**:
   ```bash
   python --version
   pip --version
   pip list | grep -E "(llama|openai)"
   ```

3. **Workaround temporaneo**:
   ```bash
   # Usa Docker per evitare conflitti
   make start
   make shell
   # Lavora nell'ambiente containerizzato
   ```

## 🎯 Prevenzione

Per evitare conflitti futuri:

1. **Usa sempre ambienti virtuali**
2. **Aggiorna regolarmente**: `make fix-deps`
3. **Testa dopo aggiornamenti**: `python -m bleen_agent.cli --help`
4. **Usa Docker per produzione**

---

**Nota**: Le dipendenze sono state aggiornate per risolvere i conflitti noti. Se incontri nuovi problemi, usa `make fix-deps` che risolve automaticamente la maggior parte dei conflitti.
