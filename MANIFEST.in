# Include package files
include README.md
include LICENSE
include ARCHITECTURE.md
include DEPENDENCY_FIX.md
include DOCKER_README.md
include .env.example

# Include package data
recursive-include bleen_agent *.py
recursive-include bleen_agent *.typed

# Include documentation
recursive-include docs *.md
recursive-include docs *.rst

# Include examples (but not the large sample documents)
include examples/*.md
include examples/*.txt

# Include Solr schema
recursive-include solr_schema *.xml
recursive-include solr_schema *.json
recursive-include solr_schema *.sh

# Exclude development and build files
exclude .gitignore
exclude .env
exclude test_dependencies.py
recursive-exclude * __pycache__
recursive-exclude * *.py[co]
recursive-exclude * *.so
recursive-exclude * .DS_Store

# Exclude DevOps files (not needed in package)
recursive-exclude devops *
recursive-exclude data *
recursive-exclude logs *

# Exclude large sample documents
recursive-exclude examples/sample_documents *

# Exclude test files
recursive-exclude tests *
exclude pytest.ini
exclude .coverage
exclude coverage.xml

# Exclude Docker files
exclude Dockerfile*
exclude docker-compose*.yml
