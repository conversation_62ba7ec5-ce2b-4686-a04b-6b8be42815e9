version: '3.8'

services:
  # Apache Solr standalone service
  solr:
    image: solr:9.4
    container_name: bleen-solr-standalone
    ports:
      - "8983:8983"
    volumes:
      - solr_standalone_data:/var/solr
      - ./solr_schema:/opt/solr-config
    environment:
      - SOLR_HEAP=1g
      - SOLR_JAVA_MEM=-Xms512m -Xmx1g
      - SOLR_LOG_LEVEL=INFO
    command: >
      bash -c "
        echo 'Starting Solr standalone for Bleen Agent...' &&
        solr start -f -p 8983 &
        SOLR_PID=$$! &&
        echo 'Waiting for Solr to start...' &&
        sleep 30 &&
        echo 'Creating bleen_documents collection...' &&
        solr create_collection -c bleen_documents -d /opt/solr-config &&
        echo 'Solr setup complete! Collection created.' &&
        echo 'Solr Admin UI available at: http://localhost:8983' &&
        echo 'Use SOLR_URL=http://localhost:8983/solr in your .env file' &&
        wait $$SOLR_PID
      "
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8983/solr/admin/ping"]
      interval: 30s
      timeout: 10s
      retries: 5
      start_period: 60s
    restart: unless-stopped
    networks:
      - bleen-solr-network

  # Optional: Solr admin interface with custom styling
  solr-admin:
    image: nginx:alpine
    container_name: bleen-solr-admin
    ports:
      - "8080:80"
    volumes:
      - ./docker/solr-admin.conf:/etc/nginx/nginx.conf:ro
    depends_on:
      solr:
        condition: service_healthy
    restart: unless-stopped
    networks:
      - bleen-solr-network

volumes:
  solr_standalone_data:
    driver: local
    driver_opts:
      type: none
      o: bind
      device: ./data/solr

networks:
  bleen-solr-network:
    driver: bridge
    name: bleen-solr-network
