# Bleen Agent Local Development Configuration
# Copy this file to .env for local development with Solr in Docker

# =============================================================================
# OpenAI Configuration (REQUIRED)
# =============================================================================
# Get your API key from: https://platform.openai.com/api-keys
OPENAI_API_KEY=your_openai_api_key_here

# =============================================================================
# Solr Configuration (Docker)
# =============================================================================
# Solr running in Docker container
SOLR_URL=http://localhost:8983/solr
SOLR_COLLECTION=bleen_documents

# =============================================================================
# Agent Configuration (Development)
# =============================================================================
# Debug logging for development
LOG_LEVEL=DEBUG

# Maximum search results to return
MAX_SEARCH_RESULTS=10

# Agent behavior (verbose for development)
AGENT_MAX_ITERATIONS=10
AGENT_VERBOSE=true

# =============================================================================
# LLM Configuration
# =============================================================================
# OpenAI model for the main agent
LLM_MODEL=gpt-4-turbo-preview

# Temperature for response generation (0.0 = deterministic, 1.0 = creative)
LLM_TEMPERATURE=0.1

# Maximum tokens for LLM responses
LLM_MAX_TOKENS=4000

# =============================================================================
# Memory Configuration
# =============================================================================
# Maximum tokens to keep in conversation memory
MEMORY_MAX_TOKENS=8000

# Model for memory summarization (can be cheaper than main model)
MEMORY_SUMMARY_MODEL=gpt-3.5-turbo

# =============================================================================
# Tool Configuration
# =============================================================================
# Timeout for tool execution (seconds)
TOOL_TIMEOUT=30

# Enable caching for tool results
ENABLE_TOOL_CACHING=true

# =============================================================================
# Document Processing Configuration
# =============================================================================
# Maximum chunk size for document splitting
MAX_CHUNK_SIZE=2000

# Overlap between chunks
CHUNK_OVERLAP=200

# Supported file types for import (comma-separated)
SUPPORTED_FILE_TYPES=.md,.txt,.pdf,.docx

# =============================================================================
# Development-Specific Configuration
# =============================================================================
# Enable development features
DEV_MODE=true

# Enable auto-reload for development (if supported)
AUTO_RELOAD=true

# Enable request logging for debugging
LOG_REQUESTS=true

# Disable response caching for development
CACHE_RESPONSES=false

# =============================================================================
# Local Development Notes
# =============================================================================
# 
# This configuration is optimized for local development where:
# - Solr runs in a Docker container (use: make solr-start)
# - Bleen Agent runs locally (use: python -m bleen_agent.cli)
# 
# Quick Start:
# 1. Copy this file to .env
# 2. Add your OpenAI API key above
# 3. Start Solr: make solr-start
# 4. Run agent: python -m bleen_agent.cli
# 
# Useful commands:
# - make solr-start     # Start Solr in Docker
# - make solr-stop      # Stop Solr
# - make solr-logs      # View Solr logs
# - make solr-health    # Check Solr status
# 
# Access Points:
# - Solr Admin UI: http://localhost:8983
# - Agent CLI: Run locally with Python
# 
# =============================================================================
