[build-system]
requires = ["setuptools>=61.0", "wheel"]
build-backend = "setuptools.build_meta"

[project]
name = "bleen-agent"
version = "0.2.0"
description = "AI Agent for Document Retrieval and Question Answering using Apache Solr"
authors = [{name = "Bleen Agent", email = "<EMAIL>"}]
license = "MIT"
readme = "README.md"
requires-python = ">=3.9"
classifiers = [
    "Development Status :: 4 - Beta",
    "Intended Audience :: Developers",
    "Programming Language :: Python :: 3",
    "Programming Language :: Python :: 3.9",
    "Programming Language :: Python :: 3.10",
    "Programming Language :: Python :: 3.11",
    "Programming Language :: Python :: 3.12",
]

# Minimal dependencies - install separately with requirements.txt
dependencies = []

[project.optional-dependencies]
dev = [
    "pytest>=8.3.2",
    "pytest-asyncio>=0.23.8",
    "black>=24.4.2",
    "isort>=5.13.2",
    "mypy>=1.11.1",
    "jupyter>=1.0.0",
]

[project.scripts]
bleen-agent = "bleen_agent.cli:main"

[tool.setuptools]
packages = ["bleen_agent"]

[tool.setuptools.package-dir]
bleen_agent = "bleen_agent"

[tool.setuptools.package-data]
bleen_agent = ["py.typed"]

[tool.black]
line-length = 88
target-version = ['py39']

[tool.isort]
profile = "black"
multi_line_output = 3

[tool.mypy]
python_version = "3.9"
warn_return_any = true
warn_unused_configs = true
disallow_untyped_defs = true
