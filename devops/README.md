# DevOps Configuration

This directory contains all DevOps-related configurations and scripts for Bleen Agent.

## Directory Structure

```
devops/
├── docker/                 # Docker configurations
│   ├── docker-compose.yml      # Production setup
│   ├── docker-compose.dev.yml  # Development setup
│   ├── docker-compose.solr-only.yml  # Solr-only setup
│   ├── Dockerfile              # Production image
│   └── Dockerfile.dev          # Development image
├── scripts/                # Automation scripts
│   ├── start.sh               # Start full stack
│   ├── stop.sh                # Stop full stack
│   ├── start-solr.sh          # Start Solr only
│   └── stop-solr.sh           # Stop Solr only
├── configs/                # Configuration files
│   └── docker/                # Docker-specific configs
│       ├── nginx.conf             # Nginx proxy config
│       └── solr-admin.conf        # Solr admin proxy config
└── README.md               # This file
```

## Usage

### Full Stack Deployment

```bash
# From project root
make start          # Production mode
make start-dev      # Development mode
make stop           # Stop services
make clean          # Stop and remove data
```

### Solr-Only Development

```bash
# From project root
make solr-start     # Start Solr container
make solr-stop      # Stop Solr container
make solr-clean     # Stop and remove data
```

### Direct Script Usage

```bash
# Full stack
./devops/scripts/start.sh --mode development --detached
./devops/scripts/stop.sh --remove-volumes

# Solr only
./devops/scripts/start-solr.sh --with-admin --detached
./devops/scripts/stop-solr.sh --remove-data
```

### Direct Docker Compose

```bash
# Production
docker-compose -f devops/docker/docker-compose.yml up -d

# Development
docker-compose -f devops/docker/docker-compose.dev.yml up -d

# Solr only
docker-compose -f devops/docker/docker-compose.solr-only.yml up -d
```

## Configurations

### Docker Compose Files

#### `docker-compose.yml` (Production)
- **Services**: Solr, Bleen Agent, Nginx proxy
- **Features**: Optimized images, health checks, production settings
- **Ports**: 8983 (Solr), 8080 (Nginx)

#### `docker-compose.dev.yml` (Development)
- **Services**: Solr, Bleen Agent, Jupyter
- **Features**: Hot reloading, debug tools, development dependencies
- **Ports**: 8983 (Solr), 8889 (Jupyter)

#### `docker-compose.solr-only.yml` (Local Development)
- **Services**: Solr standalone, optional admin proxy
- **Features**: Minimal setup for local agent development
- **Ports**: 8983 (Solr), 8080 (Admin proxy)

### Dockerfiles

#### `Dockerfile` (Production)
- Multi-stage build for optimized images
- Non-root user for security
- Health checks included
- Minimal dependencies

#### `Dockerfile.dev` (Development)
- Development tools included
- Jupyter notebook support
- Debug capabilities
- Hot reloading support

### Configuration Files

#### `configs/docker/nginx.conf`
- Nginx proxy configuration for Solr
- Security headers
- Gzip compression
- Health check endpoints

#### `configs/docker/solr-admin.conf`
- Specialized Solr admin proxy
- Enhanced error handling
- Custom status endpoints
- Optimized for Solr operations

## Scripts

### `scripts/start.sh`
**Purpose**: Start full Bleen Agent stack

**Options**:
- `--mode`: production|development
- `--agent-type`: full|researcher|librarian|analyst
- `--detached`: Run in background

**Examples**:
```bash
./devops/scripts/start.sh --mode development --agent-type researcher
./devops/scripts/start.sh --detached
```

### `scripts/stop.sh`
**Purpose**: Stop Bleen Agent services

**Options**:
- `--remove-volumes`: Remove data volumes
- `--remove-images`: Remove Docker images
- `--remove-all`: Remove volumes and images

**Examples**:
```bash
./devops/scripts/stop.sh --remove-volumes
./devops/scripts/stop.sh --remove-all
```

### `scripts/start-solr.sh`
**Purpose**: Start only Solr for local development

**Options**:
- `--with-admin`: Include admin proxy
- `--detached`: Run in background
- `--reset-data`: Start with fresh data

**Examples**:
```bash
./devops/scripts/start-solr.sh --with-admin --detached
./devops/scripts/start-solr.sh --reset-data
```

### `scripts/stop-solr.sh`
**Purpose**: Stop Solr container

**Options**:
- `--remove-data`: Remove Solr data
- `--remove-images`: Remove Docker images
- `--remove-all`: Remove data and images

**Examples**:
```bash
./devops/scripts/stop-solr.sh --remove-data
./devops/scripts/stop-solr.sh --remove-all
```

## Environment Variables

All scripts and Docker configurations use environment variables from the project root `.env` file.

**Key Variables**:
- `OPENAI_API_KEY`: Required for agent functionality
- `SOLR_URL`: Solr connection URL
- `SOLR_COLLECTION`: Collection name
- `LOG_LEVEL`: Logging verbosity
- `AGENT_VERBOSE`: Enable detailed agent logging

## Networking

### Production Network
- **Name**: `bleen-network`
- **Type**: Bridge
- **Services**: All production services

### Development Network
- **Name**: `bleen-dev-network`
- **Type**: Bridge
- **Services**: All development services

### Solr-Only Network
- **Name**: `bleen-solr-network`
- **Type**: Bridge
- **Services**: Solr and admin proxy only

## Volumes

### Production Volumes
- `solr_data`: Persistent Solr data
- `agent_data`: Application data and logs

### Development Volumes
- `solr_dev_data`: Development Solr data
- `dev_cache`: Development cache
- `jupyter_data`: Jupyter configuration

### Solr-Only Volumes
- `solr_standalone_data`: Bound to `./data/solr`

## Health Checks

All services include health checks:
- **Solr**: HTTP ping to admin endpoint
- **Agent**: Custom health check script
- **Nginx**: HTTP status endpoint

## Security

### Container Security
- Non-root users in production containers
- Minimal base images
- Security headers in proxy configurations
- Network isolation between environments

### Data Security
- Volume permissions properly configured
- Sensitive data via environment variables
- No secrets in Docker images

## Troubleshooting

### Common Issues

1. **Port Conflicts**:
   ```bash
   netstat -tulpn | grep :8983
   # Change ports in docker-compose files if needed
   ```

2. **Permission Issues**:
   ```bash
   sudo chown -R $USER:$USER ./data
   ```

3. **Volume Issues**:
   ```bash
   docker volume ls
   docker volume rm <volume_name>
   ```

4. **Network Issues**:
   ```bash
   docker network ls
   docker network prune
   ```

### Debugging

```bash
# View logs
docker logs <container_name>

# Inspect containers
docker inspect <container_name>

# Check networks
docker network inspect <network_name>

# Check volumes
docker volume inspect <volume_name>
```

## Maintenance

### Regular Tasks

1. **Update Images**:
   ```bash
   docker-compose pull
   make rebuild
   ```

2. **Clean Up**:
   ```bash
   docker system prune -f
   docker volume prune -f
   ```

3. **Backup Data**:
   ```bash
   docker run --rm -v bleen-agent_solr_data:/data -v $(pwd):/backup alpine tar czf /backup/solr-backup.tar.gz /data
   ```

This DevOps setup provides a complete, production-ready deployment solution for Bleen Agent with flexibility for different development and deployment scenarios.
