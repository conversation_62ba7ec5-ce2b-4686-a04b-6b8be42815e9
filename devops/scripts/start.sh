#!/bin/bash

# Bleen Agent Docker Startup Script
set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Default values
MODE="production"
AGENT_TYPE="full"
DETACHED=false

# Function to print colored output
print_status() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Function to show usage
show_usage() {
    echo "Usage: $0 [OPTIONS]"
    echo ""
    echo "Options:"
    echo "  -m, --mode MODE          Set mode: production, development (default: production)"
    echo "  -a, --agent-type TYPE    Set agent type: full, researcher, librarian, analyst (default: full)"
    echo "  -d, --detached          Run in detached mode"
    echo "  -h, --help              Show this help message"
    echo ""
    echo "Examples:"
    echo "  $0                                    # Start in production mode"
    echo "  $0 --mode development                # Start in development mode"
    echo "  $0 --agent-type researcher           # Start with researcher agent"
    echo "  $0 --mode development --detached     # Start dev mode in background"
}

# Parse command line arguments
while [[ $# -gt 0 ]]; do
    case $1 in
        -m|--mode)
            MODE="$2"
            shift 2
            ;;
        -a|--agent-type)
            AGENT_TYPE="$2"
            shift 2
            ;;
        -d|--detached)
            DETACHED=true
            shift
            ;;
        -h|--help)
            show_usage
            exit 0
            ;;
        *)
            print_error "Unknown option: $1"
            show_usage
            exit 1
            ;;
    esac
done

# Validate mode
if [[ "$MODE" != "production" && "$MODE" != "development" ]]; then
    print_error "Invalid mode: $MODE. Must be 'production' or 'development'"
    exit 1
fi

# Check if .env file exists
if [[ ! -f .env ]]; then
    print_warning ".env file not found. Creating from template..."
    if [[ -f .env.example ]]; then
        cp .env.example .env
        print_warning "Please edit .env file with your OpenAI API key and other settings"
    else
        print_error ".env.example file not found. Please create .env file manually"
        exit 1
    fi
fi

# Check if OpenAI API key is set
if ! grep -q "OPENAI_API_KEY=" .env || grep -q "OPENAI_API_KEY=$" .env; then
    print_error "OPENAI_API_KEY not set in .env file"
    print_error "Please add your OpenAI API key to the .env file"
    exit 1
fi

print_status "Starting Bleen Agent in $MODE mode..."
print_status "Agent type: $AGENT_TYPE"

# Set compose file based on mode
if [[ "$MODE" == "development" ]]; then
    COMPOSE_FILE="devops/docker/docker-compose.dev.yml"
    print_status "Using development configuration"
else
    COMPOSE_FILE="devops/docker/docker-compose.yml"
    print_status "Using production configuration"
fi

# Set detached flag
DETACH_FLAG=""
if [[ "$DETACHED" == true ]]; then
    DETACH_FLAG="-d"
    print_status "Running in detached mode"
fi

# Stop any existing containers
print_status "Stopping any existing containers..."
docker-compose -f $COMPOSE_FILE down --remove-orphans 2>/dev/null || true

# Build and start services
print_status "Building and starting services..."
if [[ "$MODE" == "development" ]]; then
    # Development mode
    docker-compose -f $COMPOSE_FILE up --build $DETACH_FLAG
else
    # Production mode
    AGENT_TYPE=$AGENT_TYPE docker-compose -f $COMPOSE_FILE up --build $DETACH_FLAG
fi

if [[ "$DETACHED" == true ]]; then
    print_success "Services started in detached mode"
    print_status "Solr Admin UI: http://localhost:8983"
    if [[ "$MODE" == "development" ]]; then
        print_status "Jupyter Notebook: http://localhost:8889"
    fi
    print_status "Use 'docker-compose -f $COMPOSE_FILE logs -f' to view logs"
    print_status "Use 'docker-compose -f $COMPOSE_FILE down' to stop services"
else
    print_success "Services started successfully"
fi
