#!/bin/bash

# Bleen Agent Development Environment Helper
# This script provides convenient functions for development

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
PURPLE='\033[0;35m'
CYAN='\033[0;36m'
NC='\033[0m' # No Color

# Function to print colored output
print_header() {
    echo -e "${PURPLE}[BLEEN]${NC} $1"
}

print_status() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Function to show usage
show_usage() {
    print_header "Bleen Agent Development Environment Helper"
    echo ""
    echo "Usage: $0 [COMMAND]"
    echo ""
    echo "Commands:"
    echo "  setup          - Complete development environment setup"
    echo "  status         - Check environment status"
    echo "  start          - Start development services"
    echo "  stop           - Stop development services"
    echo "  reset          - Reset environment (removes data)"
    echo "  import-samples - Import sample documents"
    echo "  shell          - Open development shell with environment"
    echo "  logs           - Show service logs"
    echo "  health         - Check service health"
    echo "  clean          - Clean up development environment"
    echo ""
    echo "Examples:"
    echo "  $0 setup       # Complete setup for new developers"
    echo "  $0 start       # Start Solr and development tools"
    echo "  $0 shell       # Open shell with dev environment loaded"
}

# Check if we're in the right directory
check_project_root() {
    if [[ ! -f "Makefile" ]] || [[ ! -d "bleen_agent" ]]; then
        print_error "This script must be run from the project root directory"
        exit 1
    fi
}

# Setup complete development environment
setup_dev_env() {
    print_header "Setting up Bleen Agent development environment"
    
    # Check prerequisites
    print_status "Checking prerequisites..."
    
    if ! command -v docker &> /dev/null; then
        print_error "Docker is required but not installed"
        exit 1
    fi
    
    if ! command -v python3 &> /dev/null; then
        print_error "Python 3 is required but not installed"
        exit 1
    fi
    
    print_success "Prerequisites check passed"
    
    # Create .env if it doesn't exist
    if [[ ! -f .env ]]; then
        print_status "Creating .env file from template..."
        cp .env.example .env
        print_warning "Please edit .env file with your OpenAI API key"
        print_status "Opening .env file for editing..."
        ${EDITOR:-nano} .env
    fi
    
    # Install Python dependencies
    print_status "Installing Python dependencies..."
    pip install -r requirements.txt
    pip install -e .
    
    # Create data directories
    print_status "Creating data directories..."
    mkdir -p data/solr logs
    
    # Start Solr
    print_status "Starting Solr container..."
    make solr-start-bg
    
    # Wait for Solr to be ready
    print_status "Waiting for Solr to be ready..."
    for i in {1..30}; do
        if curl -s http://localhost:8983/solr/admin/ping > /dev/null 2>&1; then
            break
        fi
        if [[ $i -eq 30 ]]; then
            print_error "Solr failed to start after 30 attempts"
            exit 1
        fi
        sleep 2
    done
    
    print_success "Solr is running and healthy"
    
    # Import sample documents if they exist
    if [[ -d "examples/sample_documents" ]]; then
        print_status "Importing sample documents..."
        import_samples
    fi
    
    print_success "Development environment setup complete!"
    print_status "Access points:"
    print_status "  Solr Admin UI: http://localhost:8983"
    print_status "  Start agent: python -m bleen_agent.cli"
    print_status "  Development shell: $0 shell"
}

# Check environment status
check_status() {
    print_header "Development Environment Status"
    echo ""
    
    # Check Docker
    if command -v docker &> /dev/null; then
        print_success "Docker: Installed"
    else
        print_error "Docker: Not installed"
    fi
    
    # Check Python
    if command -v python3 &> /dev/null; then
        python_version=$(python3 --version)
        print_success "Python: $python_version"
    else
        print_error "Python: Not installed"
    fi
    
    # Check Bleen Agent installation
    if python3 -c "import bleen_agent" 2>/dev/null; then
        print_success "Bleen Agent: Installed"
    else
        print_warning "Bleen Agent: Not installed (run: pip install -e .)"
    fi
    
    # Check .env file
    if [[ -f .env ]]; then
        if grep -q "your_openai_api_key_here" .env; then
            print_warning ".env: Needs OpenAI API key configuration"
        else
            print_success ".env: Configured"
        fi
    else
        print_error ".env: Missing (copy from .env.example)"
    fi
    
    # Check Solr
    if curl -s http://localhost:8983/solr/admin/ping >/dev/null 2>&1; then
        print_success "Solr: Running (http://localhost:8983)"
        
        # Check collection
        if curl -s "http://localhost:8983/solr/bleen_documents/admin/ping" >/dev/null 2>&1; then
            print_success "Solr Collection: bleen_documents exists"
        else
            print_warning "Solr Collection: bleen_documents not found"
        fi
    else
        print_warning "Solr: Not running (start with: make solr-start)"
    fi
    
    # Check data directories
    if [[ -d "data/solr" ]]; then
        data_size=$(du -sh data/solr 2>/dev/null | cut -f1 || echo "unknown")
        print_success "Data directory: data/solr ($data_size)"
    else
        print_warning "Data directory: data/solr not found"
    fi
}

# Start development services
start_services() {
    print_header "Starting development services"
    
    print_status "Starting Solr..."
    make solr-start-bg
    
    print_status "Waiting for services to be ready..."
    sleep 5
    
    make solr-health
    
    print_success "Development services started"
    print_status "Ready for development!"
}

# Stop development services
stop_services() {
    print_header "Stopping development services"
    
    make solr-stop
    
    print_success "Development services stopped"
}

# Reset development environment
reset_env() {
    print_header "Resetting development environment"
    print_warning "This will remove all Solr data!"
    
    read -p "Are you sure? (y/N): " -n 1 -r
    echo
    if [[ $REPLY =~ ^[Yy]$ ]]; then
        make solr-clean
        setup_dev_env
    else
        print_status "Reset cancelled"
    fi
}

# Import sample documents
import_samples() {
    print_status "Importing sample documents..."
    
    if [[ ! -d "examples/sample_documents" ]]; then
        print_warning "No sample documents found in examples/sample_documents"
        return
    fi
    
    python3 -c "
import asyncio
import sys
sys.path.insert(0, '.')

async def import_docs():
    try:
        from bleen_agent import create_agent_with_health_check
        agent = create_agent_with_health_check()
        if agent:
            response = await agent.ask('Import documents from examples/sample_documents')
            print('Import result:', response['answer'])
        else:
            print('❌ Agent not available. Check Solr connection.')
    except Exception as e:
        print(f'❌ Import failed: {e}')

asyncio.run(import_docs())
"
}

# Open development shell
open_dev_shell() {
    print_header "Opening development shell"
    print_status "Loading development environment..."
    
    # Source development environment
    if [[ -f "devops/configs/development.env" ]]; then
        source devops/configs/development.env
    fi
    
    print_success "Development environment loaded"
    print_status "Available commands: dev-setup, dev-status, agent-start, solr-health"
    
    # Start a new shell with the environment
    exec bash --rcfile <(echo "source devops/configs/development.env")
}

# Show service logs
show_logs() {
    print_header "Service Logs"
    make solr-logs
}

# Check service health
check_health() {
    print_header "Service Health Check"
    make solr-health
}

# Clean up development environment
clean_env() {
    print_header "Cleaning development environment"
    
    print_status "Stopping services..."
    make solr-stop 2>/dev/null || true
    
    print_status "Removing containers..."
    docker container prune -f
    
    print_status "Removing unused images..."
    docker image prune -f
    
    print_status "Removing unused volumes..."
    docker volume prune -f
    
    print_success "Development environment cleaned"
}

# Main script logic
main() {
    check_project_root
    
    case "${1:-}" in
        setup)
            setup_dev_env
            ;;
        status)
            check_status
            ;;
        start)
            start_services
            ;;
        stop)
            stop_services
            ;;
        reset)
            reset_env
            ;;
        import-samples)
            import_samples
            ;;
        shell)
            open_dev_shell
            ;;
        logs)
            show_logs
            ;;
        health)
            check_health
            ;;
        clean)
            clean_env
            ;;
        help|--help|-h)
            show_usage
            ;;
        "")
            show_usage
            ;;
        *)
            print_error "Unknown command: $1"
            show_usage
            exit 1
            ;;
    esac
}

# Run main function
main "$@"
