#!/bin/bash

# Bleen Agent - Dependency Conflict Resolution Script
set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

print_status() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

print_status "🔧 Fixing Bleen Agent dependencies..."

# Check if we're in the right directory
if [[ ! -f "requirements.txt" ]]; then
    print_error "requirements.txt not found. Run this script from the project root."
    exit 1
fi

# Backup current requirements
print_status "Creating backup of current requirements..."
cp requirements.txt requirements.txt.backup
print_success "Backup created: requirements.txt.backup"

# Uninstall conflicting packages
print_status "Uninstalling potentially conflicting packages..."
pip uninstall -y llama-index llama-index-agent-openai llama-index-llms-openai openai 2>/dev/null || true

# Clear pip cache
print_status "Clearing pip cache..."
pip cache purge

# Try different installation strategies
print_status "Trying flexible installation first..."
if [[ -f "requirements-flexible.txt" ]]; then
    print_status "Installing with flexible versions (let pip resolve conflicts)..."
    pip install -r requirements-flexible.txt
    if [[ $? -eq 0 ]]; then
        print_success "Flexible installation successful!"
    else
        print_warning "Flexible installation failed, trying minimal..."
        if [[ -f "requirements-minimal.txt" ]]; then
            pip install -r requirements-minimal.txt
        fi
    fi
else
    print_warning "requirements-flexible.txt not found, using main requirements"
    pip install -r requirements.txt
fi

# Verify installation
print_status "Verifying installation..."
python -c "
import llama_index
import openai
from llama_index.agent.openai import OpenAIAgent
from llama_index.llms.openai import OpenAI
print('✅ All core dependencies installed successfully')

# Try to get versions safely
try:
    # Try modern importlib.metadata first
    try:
        from importlib.metadata import version
        llama_version = version('llama-index')
        print(f'LlamaIndex version: {llama_version}')
    except ImportError:
        # Fallback to pkg_resources for older Python
        import pkg_resources
        llama_version = pkg_resources.get_distribution('llama-index').version
        print(f'LlamaIndex version: {llama_version}')
except:
    print('LlamaIndex version: installed (version detection unavailable)')

try:
    print(f'OpenAI version: {openai.__version__}')
except:
    print('OpenAI version: installed (version detection unavailable)')
"

# Test Bleen Agent import
print_status "Testing Bleen Agent import..."
python -c "
try:
    import bleen_agent
    print('✅ Bleen Agent imports successfully')
except ImportError as e:
    print(f'⚠️  Bleen Agent import issue: {e}')
    print('This is normal if you haven\\'t installed it yet with: pip install -e .')
"

print_success "🎉 Dependencies fixed successfully!"
print_status "Next steps:"
print_status "1. Install Bleen Agent: pip install -e ."
print_status "2. Test the installation: python -m bleen_agent.cli --help"
print_status "3. If issues persist, check the backup: requirements.txt.backup"
