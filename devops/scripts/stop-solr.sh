#!/bin/bash

# Bleen Agent - Stop Solr Only Docker Script
set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Function to print colored output
print_status() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Default values
REMOVE_DATA=false
REMOVE_IMAGES=false

# Function to show usage
show_usage() {
    echo "Usage: $0 [OPTIONS]"
    echo ""
    echo "Stop Solr Docker container for Bleen Agent"
    echo ""
    echo "Options:"
    echo "  -d, --remove-data       Remove Solr data (data will be lost)"
    echo "  -i, --remove-images     Remove Solr Docker images"
    echo "  -a, --remove-all        Remove data and images"
    echo "  -h, --help              Show this help message"
    echo ""
    echo "Examples:"
    echo "  $0                      # Stop Solr, keep data"
    echo "  $0 --remove-data        # Stop Solr and remove data"
    echo "  $0 --remove-all         # Stop Solr, remove data and images"
}

# Parse command line arguments
while [[ $# -gt 0 ]]; do
    case $1 in
        -d|--remove-data)
            REMOVE_DATA=true
            shift
            ;;
        -i|--remove-images)
            REMOVE_IMAGES=true
            shift
            ;;
        -a|--remove-all)
            REMOVE_DATA=true
            REMOVE_IMAGES=true
            shift
            ;;
        -h|--help)
            show_usage
            exit 0
            ;;
        *)
            print_error "Unknown option: $1"
            show_usage
            exit 1
            ;;
    esac
done

print_status "Stopping Solr services..."

# Check if Solr is running
if ! docker ps | grep -q "bleen-solr-standalone"; then
    print_warning "Solr container is not running"
else
    print_status "Stopping Solr container..."
    
    # Stop services
    if [[ "$REMOVE_DATA" == true ]]; then
        docker-compose -f devops/docker/docker-compose.solr-only.yml down --volumes --remove-orphans
        print_warning "Solr data has been removed"
    else
        docker-compose -f devops/docker/docker-compose.solr-only.yml down --remove-orphans
        print_status "Solr data preserved in ./data/solr"
    fi
fi

# Remove images if requested
if [[ "$REMOVE_IMAGES" == true ]]; then
    print_status "Removing Solr images..."
    
    # Remove specific images
    docker images --format "table {{.Repository}}:{{.Tag}}" | grep "solr:9.4" | awk '{print $1}' | xargs -r docker rmi -f 2>/dev/null || true
    docker images --format "table {{.Repository}}:{{.Tag}}" | grep "nginx:alpine" | awk '{print $1}' | xargs -r docker rmi -f 2>/dev/null || true
    
    # Remove dangling images
    docker image prune -f >/dev/null 2>&1 || true
    
    print_success "Images removed"
fi

print_success "Solr services stopped"

# Show remaining containers (if any)
REMAINING=$(docker ps -a --filter "name=bleen-solr" --format "table {{.Names}}\t{{.Status}}" | tail -n +2)
if [[ -n "$REMAINING" ]]; then
    print_warning "Remaining Solr containers:"
    echo "$REMAINING"
fi

# Show data directory status
if [[ -d "./data/solr" ]] && [[ "$REMOVE_DATA" != true ]]; then
    DATA_SIZE=$(du -sh ./data/solr 2>/dev/null | cut -f1 || echo "unknown")
    print_status "Solr data preserved: ./data/solr ($DATA_SIZE)"
    print_status "Use --remove-data flag to delete data on next stop"
fi

print_status "To restart Solr: ./scripts/start-solr.sh"
