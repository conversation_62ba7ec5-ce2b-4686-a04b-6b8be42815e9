#!/bin/bash

# Quick Dependency Fix Script - Simple version without version checking
set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

print_status() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

print_status "🔧 Quick fix for Bleen Agent dependencies..."

# Check if we're in the right directory
if [[ ! -f "requirements.txt" ]]; then
    print_error "requirements.txt not found. Run this script from the project root."
    exit 1
fi

# Uninstall conflicting packages
print_status "Uninstalling potentially conflicting packages..."
pip uninstall -y llama-index llama-index-agent-openai llama-index-llms-openai openai 2>/dev/null || true

# Clear pip cache
print_status "Clearing pip cache..."
pip cache purge

# Install with flexible requirements
print_status "Installing with flexible requirements..."
if [[ -f "requirements-flexible.txt" ]]; then
    pip install -r requirements-flexible.txt
else
    print_warning "requirements-flexible.txt not found, using main requirements"
    pip install -r requirements.txt
fi

# Simple verification
print_status "Verifying core imports..."
python -c "
try:
    import llama_index
    import openai
    from llama_index.agent.openai import OpenAIAgent
    from llama_index.llms.openai import OpenAI
    print('✅ All core dependencies imported successfully')
except ImportError as e:
    print(f'❌ Import error: {e}')
    exit(1)
except Exception as e:
    print(f'⚠️  Warning: {e}')
"

# Test Bleen Agent import
print_status "Testing Bleen Agent import..."
python -c "
try:
    import bleen_agent
    print('✅ Bleen Agent imports successfully')
except ImportError:
    print('⚠️  Bleen Agent not installed yet. Run: pip install -e .')
except Exception as e:
    print(f'⚠️  Bleen Agent import issue: {e}')
"

print_success "🎉 Dependencies fixed successfully!"
print_status "Next steps:"
print_status "1. Install Bleen Agent: pip install -e ."
print_status "2. Test the installation: python -m bleen_agent.cli --help"
print_status "3. Start using: make solr-start && python -m bleen_agent.cli"
