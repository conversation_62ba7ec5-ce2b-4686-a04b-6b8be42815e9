#!/bin/bash

# Bleen Agent Installation Script
# Installs dependencies and <PERSON>leen Agent without pyproject.toml issues

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

print_status() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

print_status "🚀 Installing Bleen Agent..."

# Check if we're in the right directory
if [[ ! -f "requirements.txt" ]] || [[ ! -d "bleen_agent" ]]; then
    print_error "This script must be run from the project root directory"
    exit 1
fi

# Install dependencies first
print_status "Installing dependencies..."
if [[ -f "requirements-flexible.txt" ]]; then
    print_status "Using flexible requirements..."
    pip install -r requirements-flexible.txt
else
    print_status "Using standard requirements..."
    pip install -r requirements.txt
fi

# Add current directory to Python path for development
print_status "Setting up development installation..."

# Create a simple setup.py for development installation
cat > setup_temp.py << 'EOF'
from setuptools import setup, find_packages

setup(
    name="bleen-agent",
    version="0.2.0",
    packages=["bleen_agent"],
    package_dir={"bleen_agent": "bleen_agent"},
    install_requires=[],  # Dependencies already installed
    python_requires=">=3.9",
    entry_points={
        "console_scripts": [
            "bleen-agent=bleen_agent.cli:main",
        ],
    },
)
EOF

# Install in development mode using the temporary setup.py
print_status "Installing Bleen Agent in development mode..."
pip install -e . --config-settings editable_mode=compat

# Clean up temporary file
rm -f setup_temp.py

# Verify installation
print_status "Verifying installation..."
python -c "
try:
    import bleen_agent
    from bleen_agent.cli import main
    print('✅ Bleen Agent installed successfully')
except ImportError as e:
    print(f'❌ Installation failed: {e}')
    exit(1)
"

# Test CLI
print_status "Testing CLI..."
python -m bleen_agent.cli --help > /dev/null 2>&1
if [[ $? -eq 0 ]]; then
    print_success "✅ CLI is working"
else
    print_warning "⚠️  CLI test failed, but installation may still work"
fi

print_success "🎉 Bleen Agent installation complete!"
print_status "Next steps:"
print_status "1. Start Solr: make solr-start"
print_status "2. Run agent: python -m bleen_agent.cli"
print_status "3. Or use command: bleen-agent"
