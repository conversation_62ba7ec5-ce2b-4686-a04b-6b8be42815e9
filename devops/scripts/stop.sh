#!/bin/bash

# Bleen Agent Docker Stop Script
set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Function to print colored output
print_status() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Default values
REMOVE_VOLUMES=false
REMOVE_IMAGES=false

# Function to show usage
show_usage() {
    echo "Usage: $0 [OPTIONS]"
    echo ""
    echo "Options:"
    echo "  -v, --remove-volumes     Remove volumes (data will be lost)"
    echo "  -i, --remove-images      Remove built images"
    echo "  -a, --remove-all         Remove volumes and images"
    echo "  -h, --help               Show this help message"
    echo ""
    echo "Examples:"
    echo "  $0                       # Stop services, keep data"
    echo "  $0 --remove-volumes      # Stop services and remove data"
    echo "  $0 --remove-all          # Stop services, remove data and images"
}

# Parse command line arguments
while [[ $# -gt 0 ]]; do
    case $1 in
        -v|--remove-volumes)
            REMOVE_VOLUMES=true
            shift
            ;;
        -i|--remove-images)
            REMOVE_IMAGES=true
            shift
            ;;
        -a|--remove-all)
            REMOVE_VOLUMES=true
            REMOVE_IMAGES=true
            shift
            ;;
        -h|--help)
            show_usage
            exit 0
            ;;
        *)
            print_error "Unknown option: $1"
            show_usage
            exit 1
            ;;
    esac
done

print_status "Stopping Bleen Agent services..."

# Stop production services
if [[ -f devops/docker/docker-compose.yml ]]; then
    print_status "Stopping production services..."
    if [[ "$REMOVE_VOLUMES" == true ]]; then
        docker-compose -f devops/docker/docker-compose.yml down --volumes --remove-orphans
    else
        docker-compose -f devops/docker/docker-compose.yml down --remove-orphans
    fi
fi

# Stop development services
if [[ -f devops/docker/docker-compose.dev.yml ]]; then
    print_status "Stopping development services..."
    if [[ "$REMOVE_VOLUMES" == true ]]; then
        docker-compose -f devops/docker/docker-compose.dev.yml down --volumes --remove-orphans
    else
        docker-compose -f devops/docker/docker-compose.dev.yml down --remove-orphans
    fi
fi

# Remove images if requested
if [[ "$REMOVE_IMAGES" == true ]]; then
    print_status "Removing built images..."
    
    # Remove Bleen Agent images
    docker images --format "table {{.Repository}}:{{.Tag}}" | grep "bleen-agent" | awk '{print $1}' | xargs -r docker rmi -f
    
    # Remove dangling images
    docker image prune -f
    
    print_success "Images removed"
fi

# Show status
if [[ "$REMOVE_VOLUMES" == true ]]; then
    print_warning "Data volumes have been removed"
fi

print_success "All Bleen Agent services stopped"

# Show remaining containers (if any)
REMAINING=$(docker ps -a --filter "name=bleen" --format "table {{.Names}}\t{{.Status}}" | tail -n +2)
if [[ -n "$REMAINING" ]]; then
    print_warning "Remaining containers:"
    echo "$REMAINING"
fi
