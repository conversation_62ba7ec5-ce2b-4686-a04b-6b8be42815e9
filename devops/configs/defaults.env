# DevOps Default Configuration
# These are default values used by DevOps scripts
# Override in project root .env file

# =============================================================================
# Docker Configuration
# =============================================================================
COMPOSE_PROJECT_NAME=bleen-agent
COMPOSE_FILE_PRODUCTION=devops/docker/docker-compose.yml
COMPOSE_FILE_DEVELOPMENT=devops/docker/docker-compose.dev.yml
COMPOSE_FILE_SOLR_ONLY=devops/docker/docker-compose.solr-only.yml

# =============================================================================
# Container Names
# =============================================================================
SOLR_CONTAINER_NAME=bleen-solr-standalone
SOLR_DEV_CONTAINER_NAME=bleen-solr-dev
AGENT_CONTAINER_NAME=bleen-agent-app
AGENT_DEV_CONTAINER_NAME=bleen-agent-dev
JUPYTER_CONTAINER_NAME=bleen-jupyter

# =============================================================================
# Network Configuration
# =============================================================================
PRODUCTION_NETWORK=bleen-network
DEVELOPMENT_NETWORK=bleen-dev-network
SOLR_NETWORK=bleen-solr-network

# =============================================================================
# Volume Configuration
# =============================================================================
SOLR_DATA_VOLUME=solr_data
SOLR_DEV_DATA_VOLUME=solr_dev_data
SOLR_STANDALONE_DATA_VOLUME=solr_standalone_data
AGENT_DATA_VOLUME=agent_data
DEV_CACHE_VOLUME=dev_cache
JUPYTER_DATA_VOLUME=jupyter_data

# =============================================================================
# Port Configuration
# =============================================================================
SOLR_PORT=8983
NGINX_PORT=8080
JUPYTER_PORT=8889

# =============================================================================
# Health Check Configuration
# =============================================================================
HEALTH_CHECK_INTERVAL=30s
HEALTH_CHECK_TIMEOUT=10s
HEALTH_CHECK_RETRIES=5
HEALTH_CHECK_START_PERIOD=60s

# =============================================================================
# Resource Limits (Production)
# =============================================================================
SOLR_MEMORY_LIMIT=2g
SOLR_MEMORY_RESERVATION=1g
AGENT_MEMORY_LIMIT=1g
AGENT_MEMORY_RESERVATION=512m

# =============================================================================
# Development Configuration
# =============================================================================
DEV_SOLR_MEMORY_LIMIT=1g
DEV_AGENT_MEMORY_LIMIT=512m
DEV_ENABLE_DEBUG=true
DEV_ENABLE_HOT_RELOAD=true

# =============================================================================
# Script Configuration
# =============================================================================
SCRIPT_LOG_LEVEL=INFO
SCRIPT_ENABLE_COLORS=true
SCRIPT_TIMEOUT=300

# =============================================================================
# Backup Configuration
# =============================================================================
BACKUP_DIR=./backups
BACKUP_RETENTION_DAYS=30
BACKUP_COMPRESS=true

# =============================================================================
# Monitoring Configuration
# =============================================================================
ENABLE_MONITORING=false
MONITORING_PORT=9090
METRICS_ENDPOINT=/metrics
