# Development Environment Configuration
# Source this file for development environment setup

# =============================================================================
# Development Mode Settings
# =============================================================================
export DEV_MODE=true
export LOG_LEVEL=DEBUG
export AGENT_VERBOSE=true

# =============================================================================
# Solr Development Settings
# =============================================================================
export SOLR_URL=http://localhost:8983/solr
export SOLR_COLLECTION=bleen_documents
export SOLR_HEAP=512m

# =============================================================================
# Agent Development Settings
# =============================================================================
export LLM_MODEL=gpt-4-turbo-preview
export LLM_TEMPERATURE=0.1
export MEMORY_MAX_TOKENS=8000
export MAX_SEARCH_RESULTS=10

# =============================================================================
# Development Tools
# =============================================================================
export ENABLE_HOT_RELOAD=true
export ENABLE_DEBUG_TOOLS=true
export CACHE_RESPONSES=false

# =============================================================================
# Development Aliases
# =============================================================================
alias solr-start='make solr-start-bg'
alias solr-stop='make solr-stop'
alias solr-logs='make solr-logs'
alias solr-health='make solr-health'
alias agent-start='python -m bleen_agent.cli'
alias agent-researcher='python -m bleen_agent.cli --agent-type researcher'
alias agent-librarian='python -m bleen_agent.cli --agent-type librarian'
alias agent-analyst='python -m bleen_agent.cli --agent-type analyst'

# =============================================================================
# Development Functions
# =============================================================================

# Quick development setup
dev-setup() {
    echo "🚀 Setting up Bleen Agent development environment..."
    
    # Check if .env exists
    if [ ! -f .env ]; then
        echo "📝 Creating .env from template..."
        cp .env.example .env
        echo "⚠️  Please edit .env with your OpenAI API key"
    fi
    
    # Start Solr
    echo "🔍 Starting Solr..."
    make solr-start-bg
    
    # Wait for Solr to be ready
    echo "⏳ Waiting for Solr to be ready..."
    sleep 10
    
    # Check Solr health
    make solr-health
    
    echo "✅ Development environment ready!"
    echo "💡 Run 'agent-start' to start the agent"
}

# Quick development teardown
dev-teardown() {
    echo "🛑 Tearing down development environment..."
    make solr-stop
    echo "✅ Development environment stopped"
}

# Reset development environment
dev-reset() {
    echo "🔄 Resetting development environment..."
    make solr-clean
    dev-setup
}

# Import sample documents
dev-import-samples() {
    echo "📚 Importing sample documents..."
    if [ -d "examples/sample_documents" ]; then
        python -c "
import asyncio
from bleen_agent import create_agent_with_health_check

async def import_samples():
    agent = create_agent_with_health_check()
    if agent:
        response = await agent.ask('Import documents from examples/sample_documents')
        print(response['answer'])
    else:
        print('❌ Agent not available. Check Solr connection.')

asyncio.run(import_samples())
"
    else
        echo "❌ Sample documents directory not found"
    fi
}

# Development status check
dev-status() {
    echo "📊 Development Environment Status"
    echo "================================="
    
    # Check Solr
    if curl -s http://localhost:8983/solr/admin/ping >/dev/null 2>&1; then
        echo "✅ Solr: Running (http://localhost:8983)"
    else
        echo "❌ Solr: Not running"
    fi
    
    # Check .env file
    if [ -f .env ]; then
        if grep -q "OPENAI_API_KEY=your_" .env; then
            echo "⚠️  .env: Needs OpenAI API key"
        else
            echo "✅ .env: Configured"
        fi
    else
        echo "❌ .env: Missing"
    fi
    
    # Check Python environment
    if python -c "import bleen_agent" 2>/dev/null; then
        echo "✅ Python: Bleen Agent installed"
    else
        echo "❌ Python: Bleen Agent not installed"
    fi
    
    echo ""
    echo "💡 Quick commands:"
    echo "   dev-setup     - Setup development environment"
    echo "   dev-status    - Check environment status"
    echo "   dev-reset     - Reset environment"
    echo "   agent-start   - Start agent CLI"
    echo "   solr-health   - Check Solr health"
}

# Export functions
export -f dev-setup
export -f dev-teardown
export -f dev-reset
export -f dev-import-samples
export -f dev-status

# Welcome message
echo "🤖 Bleen Agent Development Environment Loaded"
echo "💡 Run 'dev-status' to check environment status"
echo "🚀 Run 'dev-setup' to initialize development environment"
