# Development Dockerfile for Bleen Agent
FROM python:3.11-slim

# Set environment variables
ENV PYTHONDONTWRITEBYTECODE=1 \
    PYTHONUNBUFFERED=1 \
    PIP_NO_CACHE_DIR=1

# Install system dependencies
RUN apt-get update && apt-get install -y \
    build-essential \
    curl \
    git \
    vim \
    && rm -rf /var/lib/apt/lists/*

# Set work directory
WORKDIR /app

# Install Python dependencies
COPY requirements.txt .
RUN pip install --upgrade pip && \
    pip install -r requirements.txt && \
    pip install jupyter ipython pytest pytest-cov black isort mypy

# Copy application code
COPY . .

# Install in development mode
RUN pip install -e .

# Create directories
RUN mkdir -p /app/data /app/logs

# Expose ports for development
EXPOSE 8888 5000

# Default command for development
CMD ["python", "-m", "bleen_agent.cli", "--agent-type", "full", "--verbose"]
