version: '3.8'

services:
  # Apache Solr for development
  solr-dev:
    image: solr:9.4
    container_name: bleen-solr-dev
    ports:
      - "8983:8983"
    volumes:
      - solr_dev_data:/var/solr
      - ../../solr_schema:/opt/solr-config
    environment:
      - SOLR_HEAP=512m
      - SOLR_JAVA_MEM=-Xms256m -Xmx512m
      - SOLR_LOG_LEVEL=INFO
    command: >
      bash -c "
        echo 'Starting Solr in development mode...' &&
        solr start -f -p 8983 &
        sleep 30 &&
        echo 'Creating collection...' &&
        solr create_collection -c bleen_documents -d /opt/solr-config &&
        wait
      "
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8983/solr/admin/ping"]
      interval: 15s
      timeout: 5s
      retries: 10
      start_period: 45s
    networks:
      - bleen-dev-network

  # Bleen Agent development environment
  bleen-agent-dev:
    build:
      context: ../../
      dockerfile: devops/docker/Dockerfile.dev
    container_name: bleen-agent-dev
    depends_on:
      solr-dev:
        condition: service_healthy
    environment:
      - SOLR_URL=http://solr-dev:8983/solr
      - SOLR_COLLECTION=bleen_documents
      - OPENAI_API_KEY=${OPENAI_API_KEY}
      - LOG_LEVEL=DEBUG
      - AGENT_VERBOSE=true
      - PYTHONPATH=/app
    volumes:
      - ../../:/app
      - dev_cache:/root/.cache
    ports:
      - "8888:8888"  # Jupyter
      - "5000:5000"  # Flask (if needed)
    stdin_open: true
    tty: true
    networks:
      - bleen-dev-network
    command: >
      bash -c "
        echo 'Development environment ready!' &&
        echo 'Available commands:' &&
        echo '  python -m bleen_agent.cli --help' &&
        echo '  jupyter notebook --ip=0.0.0.0 --port=8888 --no-browser --allow-root' &&
        echo '  pytest' &&
        echo '' &&
        bash
      "

  # Optional: Jupyter notebook service
  jupyter:
    build:
      context: ../../
      dockerfile: devops/docker/Dockerfile.dev
    container_name: bleen-jupyter
    depends_on:
      - solr-dev
    environment:
      - SOLR_URL=http://solr-dev:8983/solr
      - SOLR_COLLECTION=bleen_documents
      - OPENAI_API_KEY=${OPENAI_API_KEY}
    volumes:
      - ../../:/app
      - jupyter_data:/root/.jupyter
    ports:
      - "8889:8888"
    networks:
      - bleen-dev-network
    command: >
      bash -c "
        echo 'Starting Jupyter notebook...' &&
        jupyter notebook --ip=0.0.0.0 --port=8888 --no-browser --allow-root --NotebookApp.token='' --NotebookApp.password=''
      "

volumes:
  solr_dev_data:
    driver: local
  dev_cache:
    driver: local
  jupyter_data:
    driver: local

networks:
  bleen-dev-network:
    driver: bridge
