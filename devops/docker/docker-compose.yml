version: '3.8'

services:
  # Apache Solr service
  solr:
    image: solr:9.4
    container_name: bleen-solr
    ports:
      - "8983:8983"
    volumes:
      - solr_data:/var/solr
      - ../../solr_schema:/opt/solr-config
    environment:
      - SOLR_HEAP=1g
      - SOLR_JAVA_MEM=-Xms512m -Xmx1g
    command: >
      bash -c "
        solr-precreate bleen_documents /opt/solr-config &&
        solr-foreground
      "
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8983/solr/admin/ping"]
      interval: 30s
      timeout: 10s
      retries: 5
      start_period: 60s
    networks:
      - bleen-network

  # Bleen Agent application
  bleen-agent:
    build:
      context: ../../
      dockerfile: devops/docker/Dockerfile
    container_name: bleen-agent-app
    depends_on:
      solr:
        condition: service_healthy
    environment:
      - SOLR_URL=http://solr:8983/solr
      - SOLR_COLLECTION=bleen_documents
      - OPENAI_API_KEY=${OPENAI_API_KEY}
      - LOG_LEVEL=INFO
      - AGENT_VERBOSE=true
    volumes:
      - ../../examples:/app/examples
      - ../../docs:/app/docs
      - agent_data:/app/data
    stdin_open: true
    tty: true
    networks:
      - bleen-network
    command: python -m bleen_agent.cli --agent-type full

  # Optional: Solr Admin UI (separate lightweight container)
  solr-admin:
    image: nginx:alpine
    container_name: bleen-solr-admin
    ports:
      - "8080:80"
    volumes:
      - ../configs/docker/nginx.conf:/etc/nginx/nginx.conf:ro
    depends_on:
      - solr
    networks:
      - bleen-network

volumes:
  solr_data:
    driver: local
  agent_data:
    driver: local

networks:
  bleen-network:
    driver: bridge
