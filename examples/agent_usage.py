#!/usr/bin/env python3
"""
Example usage of Bleen Agent with the new tool-based architecture.

This example demonstrates:
1. Creating different types of agents
2. Using agents programmatically
3. Working with tools directly
4. Managing conversation memory
"""

import asyncio
import json
from pathlib import Path

# Import Bleen Agent components
from bleen_agent import (
    create_agent,
    create_preset_agent, 
    create_agent_with_health_check,
    AGENT_PRESETS,
    get_all_tools
)


async def basic_agent_usage():
    """Basic agent usage example."""
    print("=== Basic Agent Usage ===")
    
    # Create a full-featured agent with health check
    agent = create_agent_with_health_check()
    
    if not agent:
        print("❌ Failed to create agent - check Solr connection")
        return
    
    print(f"✅ Agent created with session ID: {agent.session_id}")
    
    # Ask a question
    response = await agent.ask("What tools do you have available?")
    print(f"Agent: {response['answer']}")
    print(f"Confidence: {response['confidence']:.2f}")
    print(f"Tools used: {response['metadata']['tools_used']}")
    
    # Show agent status
    status = agent.get_agent_status()
    print(f"Available tools: {len(status['tools_available'])}")
    
    return agent


async def preset_agents_example():
    """Example of using preset agent configurations."""
    print("\n=== Preset Agents Example ===")
    
    # Show available presets
    print("Available presets:")
    for preset_name, config in AGENT_PRESETS.items():
        print(f"  - {preset_name}: {config['description']}")
    
    # Create a researcher agent
    researcher = create_preset_agent("researcher")
    print(f"✅ Researcher agent created with {len(researcher.tools)} tools")
    
    # Create a librarian agent
    librarian = create_preset_agent("librarian")
    print(f"✅ Librarian agent created with {len(librarian.tools)} tools")
    
    # Compare their capabilities
    researcher_tools = {tool.metadata.name for tool in researcher.tools}
    librarian_tools = {tool.metadata.name for tool in librarian.tools}
    
    print(f"Researcher tools: {researcher_tools}")
    print(f"Librarian tools: {librarian_tools}")
    print(f"Common tools: {researcher_tools & librarian_tools}")
    
    return researcher, librarian


async def document_management_example(agent):
    """Example of document management operations."""
    print("\n=== Document Management Example ===")
    
    # Create a sample document
    sample_doc_path = Path("examples/sample_document.md")
    sample_doc_path.parent.mkdir(exist_ok=True)
    
    sample_content = """---
title: "Sample Document"
author: "Bleen Agent"
category: ["example", "documentation"]
tags: ["sample", "test", "markdown"]
---

# Sample Document

This is a sample document for testing Bleen Agent.

## Features

- Document parsing
- Metadata extraction
- Full-text search
- Tool-based architecture

## Conclusion

This document demonstrates the capabilities of Bleen Agent.
"""
    
    with open(sample_doc_path, "w") as f:
        f.write(sample_content)
    
    print(f"📄 Created sample document: {sample_doc_path}")
    
    # Import the document
    response = await agent.ask(f"Import the document from {sample_doc_path}")
    print(f"Import result: {response['answer']}")
    
    # Get collection statistics
    response = await agent.ask("Show me the collection statistics")
    print(f"Collection stats: {response['answer']}")
    
    # Search for the document
    response = await agent.ask("Search for documents about 'Bleen Agent'")
    print(f"Search results: {response['answer']}")
    
    return sample_doc_path


async def conversation_memory_example(agent):
    """Example of conversation memory and context."""
    print("\n=== Conversation Memory Example ===")
    
    # Ask a series of related questions
    questions = [
        "What is machine learning?",
        "How does it relate to artificial intelligence?", 
        "Can you give me examples of applications?",
        "What did we discuss about machine learning earlier?"
    ]
    
    for i, question in enumerate(questions, 1):
        print(f"\nQuestion {i}: {question}")
        response = await agent.ask(question)
        print(f"Answer: {response['answer'][:200]}...")
        print(f"Confidence: {response['confidence']:.2f}")
    
    # Show conversation history
    history = agent.get_conversation_history()
    print(f"\nConversation history: {len(history)} interactions")
    
    # Show memory usage
    memory_stats = agent.memory.get_usage_stats()
    print(f"Memory usage: {memory_stats['current_tokens']}/{memory_stats['max_tokens']} tokens")
    print(f"Memory utilization: {memory_stats['memory_utilization']:.1%}")


async def tool_analysis_example(agent):
    """Example of analyzing queries and tool selection."""
    print("\n=== Tool Analysis Example ===")
    
    # Ask the agent to analyze different types of queries
    queries = [
        "What is the definition of artificial intelligence?",
        "Find documents by John Doe about machine learning",
        "Show me documents similar to doc_123",
        "Import documents from /path/to/docs",
        "What are the collection statistics?"
    ]
    
    for query in queries:
        print(f"\nAnalyzing query: '{query}'")
        response = await agent.ask(f"Analyze this query and recommend the best approach: {query}")
        print(f"Analysis: {response['answer'][:300]}...")


async def error_handling_example():
    """Example of error handling and recovery."""
    print("\n=== Error Handling Example ===")
    
    # Try to create agent without Solr
    print("Testing agent creation without Solr...")
    agent = create_agent_with_health_check()
    
    if not agent:
        print("❌ Agent creation failed as expected")
        
        # Create agent without health check (will fail on first tool use)
        print("Creating agent without health check...")
        agent = create_agent("search")
        
        try:
            response = await agent.ask("Search for anything")
            print(f"Unexpected success: {response['answer']}")
        except Exception as e:
            print(f"❌ Expected error: {str(e)}")
    
    return agent


async def main():
    """Main example function."""
    print("🤖 Bleen Agent - Tool-Based Architecture Examples")
    print("=" * 50)
    
    try:
        # Basic usage
        agent = await basic_agent_usage()
        
        if agent:
            # Preset agents
            researcher, librarian = await preset_agents_example()
            
            # Document management
            sample_doc = await document_management_example(agent)
            
            # Conversation memory
            await conversation_memory_example(agent)
            
            # Tool analysis
            await tool_analysis_example(agent)
            
            # Cleanup
            if sample_doc and sample_doc.exists():
                sample_doc.unlink()
                print(f"🗑️ Cleaned up sample document: {sample_doc}")
        
        # Error handling
        await error_handling_example()
        
    except Exception as e:
        print(f"❌ Example failed: {str(e)}")
    
    print("\n✅ Examples completed!")


if __name__ == "__main__":
    # Run the examples
    asyncio.run(main())
