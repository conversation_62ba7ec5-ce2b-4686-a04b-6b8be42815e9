#!/usr/bin/env python3
"""
Quick test script to verify all dependencies are working correctly.
Run this after installing dependencies to ensure everything is compatible.
"""

import sys
import importlib
from typing import List, <PERSON><PERSON>

def test_import(module_name: str, description: str = "") -> Tuple[bool, str]:
    """Test if a module can be imported successfully."""
    try:
        importlib.import_module(module_name)
        return True, f"✅ {module_name} - {description}"
    except ImportError as e:
        return False, f"❌ {module_name} - {description}: {str(e)}"
    except Exception as e:
        return False, f"⚠️  {module_name} - {description}: {str(e)}"

def test_version_compatibility() -> List[Tuple[bool, str]]:
    """Test version compatibility of key packages."""
    results = []
    
    try:
        import openai
        import llama_index
        
        openai_version = openai.__version__
        llama_version = llama_index.__version__
        
        # Check OpenAI version is >= 1.40.0
        openai_major, openai_minor = map(int, openai_version.split('.')[:2])
        if openai_major > 1 or (openai_major == 1 and openai_minor >= 40):
            results.append((True, f"✅ OpenAI version {openai_version} is compatible"))
        else:
            results.append((False, f"❌ OpenAI version {openai_version} is too old (need >= 1.40.0)"))
        
        results.append((True, f"✅ LlamaIndex version {llama_version}"))
        
    except Exception as e:
        results.append((False, f"❌ Version check failed: {str(e)}"))
    
    return results

def main():
    """Run all dependency tests."""
    print("🔍 Testing Bleen Agent Dependencies")
    print("=" * 50)
    
    # Core dependencies
    core_tests = [
        ("llama_index", "LlamaIndex core"),
        ("openai", "OpenAI API client"),
        ("llama_index.agent.openai", "LlamaIndex OpenAI agent"),
        ("llama_index.llms.openai", "LlamaIndex OpenAI LLM"),
        ("llama_index.tools.requests", "LlamaIndex requests tool"),
    ]
    
    # Document processing
    doc_tests = [
        ("pysolr", "Apache Solr client"),
        ("frontmatter", "Frontmatter parser"),
        ("tiktoken", "OpenAI tokenizer"),
        ("markdown", "Markdown processor"),
    ]
    
    # CLI and utilities
    util_tests = [
        ("click", "CLI framework"),
        ("rich", "Rich text formatting"),
        ("dotenv", "Environment variables"),
        ("pydantic", "Data validation"),
        ("requests", "HTTP client"),
    ]
    
    # Bleen Agent
    bleen_tests = [
        ("bleen_agent", "Bleen Agent core"),
        ("bleen_agent.agent", "Bleen Agent agent module"),
        ("bleen_agent.tools", "Bleen Agent tools"),
        ("bleen_agent.cli", "Bleen Agent CLI"),
    ]
    
    all_tests = [
        ("Core Dependencies", core_tests),
        ("Document Processing", doc_tests),
        ("CLI and Utilities", util_tests),
        ("Bleen Agent", bleen_tests),
    ]
    
    total_tests = 0
    passed_tests = 0
    
    for category, tests in all_tests:
        print(f"\n📦 {category}")
        print("-" * 30)
        
        for module_name, description in tests:
            success, message = test_import(module_name, description)
            print(message)
            total_tests += 1
            if success:
                passed_tests += 1
    
    # Version compatibility tests
    print(f"\n🔢 Version Compatibility")
    print("-" * 30)
    
    version_results = test_version_compatibility()
    for success, message in version_results:
        print(message)
        total_tests += 1
        if success:
            passed_tests += 1
    
    # Summary
    print(f"\n📊 Summary")
    print("=" * 50)
    print(f"Total tests: {total_tests}")
    print(f"Passed: {passed_tests}")
    print(f"Failed: {total_tests - passed_tests}")
    
    if passed_tests == total_tests:
        print("🎉 All dependencies are working correctly!")
        return 0
    else:
        print("⚠️  Some dependencies have issues. Check the output above.")
        print("\n💡 Try running: make fix-deps")
        return 1

if __name__ == "__main__":
    sys.exit(main())
