[build-system]
requires = ["setuptools>=61.0", "wheel"]
build-backend = "setuptools.build_meta"

[project]
name = "bleen-agent"
version = "0.1.0"
description = "AI Agent for Document Retrieval and Question Answering using Apache Solr"
authors = [{name = "Bleen Agent", email = "<EMAIL>"}]
license = {text = "MIT"}
readme = "README.md"
requires-python = ">=3.9"
classifiers = [
    "Development Status :: 3 - Alpha",
    "Intended Audience :: Developers",
    "License :: OSI Approved :: MIT License",
    "Programming Language :: Python :: 3",
    "Programming Language :: Python :: 3.9",
    "Programming Language :: Python :: 3.10",
    "Programming Language :: Python :: 3.11",
]

dependencies = [
    "llama-index>=0.9.48",
    "llama-index-vector-stores-solr>=0.1.0",
    "openai>=1.3.8",
    "pysolr>=3.9.0",
    "markdown>=3.5.1",
    "python-frontmatter>=1.0.0",
    "tiktoken>=0.5.2",
    "click>=8.1.7",
    "rich>=13.7.0",
    "python-dotenv>=1.0.0",
    "structlog>=23.2.0",
    "pydantic>=2.5.2",
    "pydantic-settings>=2.1.0",
]

[project.optional-dependencies]
dev = [
    "pytest>=7.4.3",
    "pytest-asyncio>=0.21.1",
    "black>=23.11.0",
    "isort>=5.12.0",
    "mypy>=1.7.1",
]

[project.scripts]
bleen-agent = "bleen_agent.cli:main"
bleen-import = "bleen_agent.import_docs:main"

[tool.black]
line-length = 88
target-version = ['py39']

[tool.isort]
profile = "black"
multi_line_output = 3

[tool.mypy]
python_version = "3.9"
warn_return_any = true
warn_unused_configs = true
disallow_untyped_defs = true
