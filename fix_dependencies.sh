#!/bin/bash

# Fix Dependencies Script
set -e

echo "🔧 Fixing Bleen Agent dependencies..."

# Uninstall problematic packages
echo "Removing conflicting packages..."
pip uninstall -y requests urllib3 charset-normalizer beautifulsoup4 2>/dev/null || true
pip uninstall -y llama-index llama-index-agent-openai llama-index-llms-openai openai 2>/dev/null || true

# Clear pip cache
echo "Clearing pip cache..."
pip cache purge

# Install specific compatible versions
echo "Installing compatible versions..."
pip install urllib3==1.26.18
pip install charset-normalizer==3.3.2
pip install requests==2.32.3

# Install core dependencies
echo "Installing core dependencies..."
pip install -r requirements.txt

# Install Bleen Agent
echo "Installing Bleen Agent..."
pip install -e .

# Test installation
echo "Testing installation..."
python -c "
try:
    import bleen_agent
    print('✅ Bleen Agent installed successfully')
except Exception as e:
    print(f'❌ Error: {e}')
    exit(1)
"

echo "🎉 Dependencies fixed successfully!"
echo "Now you can run: python -m bleen_agent.cli"
