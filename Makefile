# Bleen Agent Makefile
# Simplified commands for Solr and local development

.PHONY: help setup solr-start solr-stop solr-logs install test

# Default target
help:
	@echo "Bleen Agent - Available Commands:"
	@echo ""
	@echo "Setup and Installation:"
	@echo "  setup          - Initial setup (copy .env, install dependencies)"
	@echo "  install        - Install Python dependencies"
	@echo "  install-dev    - Install development dependencies"
	@echo ""
	@echo "Solr (Docker):"
	@echo "  solr-start     - Start Solr in Docker"
	@echo "  solr-stop      - Stop Solr container"
	@echo "  solr-logs      - Show Solr logs"
	@echo "  solr-clean     - Stop Solr and remove data"
	@echo "  solr-shell     - Open shell in Solr container"
	@echo ""
	@echo "Development:"
	@echo "  test           - Run tests"
	@echo "  lint           - Run linting checks"
	@echo "  format         - Format code"
	@echo ""
	@echo "Quick Start:"
	@echo "  quickstart     - Complete setup and start Solr"

# Setup and Installation
setup:
	@echo "Setting up Bleen Agent..."
	@if [ ! -f .env ]; then \
		cp .env.example .env; \
		echo "Created .env file from template"; \
		echo "Please edit .env with your OpenAI API key"; \
	else \
		echo ".env file already exists"; \
	fi
	@mkdir -p data logs
	@echo "Setup complete!"

install:
	@echo "Installing dependencies..."
	pip install -r requirements.txt
	pip install -e .

install-dev:
	@echo "Installing development dependencies..."
	pip install -r requirements.txt
	pip install -e .
	pip install pytest pytest-cov black isort mypy jupyter

# Solr Operations
solr-start:
	@echo "Starting Solr..."
	docker-compose up -d

solr-stop:
	@echo "Stopping Solr..."
	docker-compose down

solr-logs:
	@echo "Showing Solr logs..."
	docker-compose logs -f

solr-clean:
	@echo "Stopping Solr and removing data..."
	docker-compose down -v

solr-shell:
	@echo "Opening shell in Solr container..."
	docker exec -it bleen-solr bash

solr-restart:
	@echo "Restarting Solr..."
	docker-compose restart

# Development
test:
	@echo "Running tests..."
	pytest tests/ -v

lint:
	@echo "Running linting checks..."
	black --check bleen_agent/
	isort --check-only bleen_agent/

format:
	@echo "Formatting code..."
	black bleen_agent/
	isort bleen_agent/

# Quick start for new users
quickstart: setup solr-start
	@echo ""
	@echo "🎉 Bleen Agent setup complete!"
	@echo ""
	@echo "Next steps:"
	@echo "1. Edit .env file with your OpenAI API key"
	@echo "2. Wait for Solr to start (check with 'make solr-logs')"
	@echo "3. Access Solr Admin UI: http://localhost:8983"
	@echo "4. Run the agent: python -m bleen_agent.cli"
	@echo ""
	@echo "Use 'make help' to see all available commands"

# Check status
status:
	@echo "Service Status:"
	@echo "==============="
	@if docker ps | grep -q "bleen-solr"; then \
		echo "✅ Solr: Running (http://localhost:8983)"; \
	else \
		echo "❌ Solr: Not running"; \
	fi
	@if python -c "import bleen_agent" 2>/dev/null; then \
		echo "✅ Bleen Agent: Installed"; \
	else \
		echo "❌ Bleen Agent: Not installed"; \
	fi

# Health check
health:
	@echo "Health Check:"
	@echo "============="
	@if curl -s http://localhost:8983/solr/admin/ping >/dev/null 2>&1; then \
		echo "✅ Solr is healthy"; \
	else \
		echo "❌ Solr is not responding"; \
	fi
