# Bleen Agent Makefile
# Simplifies common development and deployment tasks

.PHONY: help setup start start-dev stop clean build test lint format install docs

# Default target
help:
	@echo "Bleen Agent - Available Commands:"
	@echo ""
	@echo "Setup and Installation:"
	@echo "  setup          - Initial setup (copy .env, install dependencies)"
	@echo "  install        - Install Python dependencies"
	@echo "  install-dev    - Install development dependencies"
	@echo ""
	@echo "Docker Operations:"
	@echo "  start          - Start production services"
	@echo "  start-dev      - Start development services"
	@echo "  start-bg       - Start services in background"
	@echo "  stop           - Stop all services"
	@echo "  clean          - Stop services and remove volumes"
	@echo "  build          - Build Docker images"
	@echo "  rebuild        - Rebuild Docker images from scratch"
	@echo ""
	@echo "Development:"
	@echo "  test           - Run tests"
	@echo "  test-cov       - Run tests with coverage"
	@echo "  lint           - Run linting checks"
	@echo "  format         - Format code"
	@echo "  type-check     - Run type checking"
	@echo ""
	@echo "Documentation:"
	@echo "  docs           - Generate documentation"
	@echo "  docs-serve     - Serve documentation locally"
	@echo ""
	@echo "Utilities:"
	@echo "  logs           - Show service logs"
	@echo "  shell          - Open shell in agent container"
	@echo "  solr-shell     - Open shell in Solr container"
	@echo "  status         - Show service status"

# Setup and Installation
setup:
	@echo "Setting up Bleen Agent..."
	@if [ ! -f .env ]; then \
		cp .env.example .env; \
		echo "Created .env file from template"; \
		echo "Please edit .env with your OpenAI API key"; \
	else \
		echo ".env file already exists"; \
	fi
	@mkdir -p data logs
	@echo "Setup complete!"

install:
	pip install -r requirements.txt

install-dev:
	pip install -r requirements.txt
	pip install pytest pytest-cov black isort mypy jupyter

# Docker Operations
start:
	@echo "Starting Bleen Agent in production mode..."
	./scripts/start.sh

start-dev:
	@echo "Starting Bleen Agent in development mode..."
	./scripts/start.sh --mode development

start-bg:
	@echo "Starting Bleen Agent in background..."
	./scripts/start.sh --detached

stop:
	@echo "Stopping Bleen Agent services..."
	./scripts/stop.sh

clean:
	@echo "Cleaning up Bleen Agent (removing volumes)..."
	./scripts/stop.sh --remove-volumes

build:
	@echo "Building Docker images..."
	docker-compose build

rebuild:
	@echo "Rebuilding Docker images from scratch..."
	docker-compose build --no-cache

# Development
test:
	@echo "Running tests..."
	pytest tests/ -v

test-cov:
	@echo "Running tests with coverage..."
	pytest tests/ -v --cov=bleen_agent --cov-report=html --cov-report=term

lint:
	@echo "Running linting checks..."
	black --check bleen_agent/
	isort --check-only bleen_agent/
	mypy bleen_agent/

format:
	@echo "Formatting code..."
	black bleen_agent/
	isort bleen_agent/

type-check:
	@echo "Running type checking..."
	mypy bleen_agent/

# Documentation
docs:
	@echo "Generating documentation..."
	@if [ -d docs/ ]; then \
		echo "Documentation directory exists"; \
	else \
		mkdir -p docs/; \
	fi
	@echo "Documentation generated in docs/"

docs-serve:
	@echo "Serving documentation locally..."
	@if command -v python3 >/dev/null 2>&1; then \
		cd docs && python3 -m http.server 8000; \
	else \
		cd docs && python -m http.server 8000; \
	fi

# Utilities
logs:
	@echo "Showing service logs..."
	@if [ -f docker-compose.yml ] && docker-compose ps | grep -q "Up"; then \
		docker-compose logs -f; \
	elif [ -f docker-compose.dev.yml ] && docker-compose -f docker-compose.dev.yml ps | grep -q "Up"; then \
		docker-compose -f docker-compose.dev.yml logs -f; \
	else \
		echo "No running services found"; \
	fi

shell:
	@echo "Opening shell in agent container..."
	@if docker ps | grep -q "bleen-agent"; then \
		docker exec -it bleen-agent-app bash; \
	elif docker ps | grep -q "bleen-agent-dev"; then \
		docker exec -it bleen-agent-dev bash; \
	else \
		echo "No agent container running"; \
	fi

solr-shell:
	@echo "Opening shell in Solr container..."
	@if docker ps | grep -q "bleen-solr"; then \
		docker exec -it bleen-solr bash; \
	elif docker ps | grep -q "bleen-solr-dev"; then \
		docker exec -it bleen-solr-dev bash; \
	else \
		echo "No Solr container running"; \
	fi

status:
	@echo "Service Status:"
	@echo "==============="
	@if [ -f docker-compose.yml ]; then \
		echo "Production services:"; \
		docker-compose ps; \
	fi
	@if [ -f docker-compose.dev.yml ]; then \
		echo "Development services:"; \
		docker-compose -f docker-compose.dev.yml ps; \
	fi

# Agent-specific commands
agent-researcher:
	@echo "Starting researcher agent..."
	./scripts/start.sh --agent-type researcher

agent-librarian:
	@echo "Starting librarian agent..."
	./scripts/start.sh --agent-type librarian

agent-analyst:
	@echo "Starting analyst agent..."
	./scripts/start.sh --agent-type analyst

# Import sample documents
import-samples:
	@echo "Importing sample documents..."
	@if docker ps | grep -q "bleen-agent"; then \
		docker exec -it bleen-agent-app python -c "import asyncio; from bleen_agent import create_agent; agent = create_agent(); asyncio.run(agent.ask('Import documents from /app/examples/sample_documents'))"; \
	else \
		echo "Agent container not running. Start with 'make start' first."; \
	fi

# Health checks
health:
	@echo "Checking service health..."
	@echo "Solr: $$(curl -s http://localhost:8983/solr/admin/ping | grep -o '"status":"OK"' || echo 'Not responding')"
	@if docker ps | grep -q "bleen-agent"; then \
		echo "Agent: Running"; \
	else \
		echo "Agent: Not running"; \
	fi

# Quick start for new users
quickstart: setup start
	@echo ""
	@echo "🎉 Bleen Agent is starting up!"
	@echo ""
	@echo "Next steps:"
	@echo "1. Edit .env file with your OpenAI API key"
	@echo "2. Wait for services to start (check with 'make status')"
	@echo "3. Access Solr Admin UI: http://localhost:8983"
	@echo "4. Import sample documents: 'make import-samples'"
	@echo "5. Start asking questions!"
	@echo ""
	@echo "Use 'make help' to see all available commands"
