events {
    worker_connections 1024;
}

http {
    upstream solr {
        server solr:8983;
    }

    server {
        listen 80;
        server_name localhost;

        location / {
            proxy_pass http://solr;
            proxy_set_header Host $host;
            proxy_set_header X-Real-IP $remote_addr;
            proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
            proxy_set_header X-Forwarded-Proto $scheme;
        }

        # Health check endpoint
        location /health {
            access_log off;
            return 200 "healthy\n";
            add_header Content-Type text/plain;
        }
    }
}
