# Bleen Agent

An AI-powered document retrieval and question answering system built with LlamaIndex, using Apache Solr for document storage and a sophisticated tool-based agent architecture.

## Features

- **Tool-Based Agent Architecture**: Built following best practices from Anthropic and OpenAI guides
- **Intelligent Document Processing**: Automatically parses Markdown and text files, extracting structured metadata
- **Full-Text Search**: Transparent keyword-based search without embeddings for better explainability
- **Smart Document Management**: Import, organize, and analyze document collections
- **Conversation Memory**: Advanced memory management with automatic summarization
- **Interactive CLI**: Chat-like interface with detailed agent reasoning visibility
- **Flexible Agent Types**: Multiple agent configurations for different use cases (researcher, librarian, analyst)

## Quick Start

### Prerequisites

- Python 3.9+
- Apache Solr 9.0+
- OpenAI API key

### Installation

1. **Clone the repository**:
   ```bash
   git clone <repository-url>
   cd bleen-agent
   ```

2. **Install dependencies**:
   ```bash
   pip install -r requirements.txt
   # or
   pip install -e .
   ```

3. **Set up environment variables**:
   ```bash
   cp .env.example .env
   # Edit .env with your configuration
   ```

4. **Start Apache Solr**:
   ```bash
   # Download and start Solr (if not already running)
   bin/solr start
   ```

5. **Create Solr collection**:
   ```bash
   cd solr_schema
   ./setup_collection.sh
   ```

### Basic Usage

1. **Start the agent**:
   ```bash
   python -m bleen_agent.cli
   ```

2. **Import documents** (within the CLI):
   ```
   You: /import ./docs
   Agent: I'll import the documents from ./docs directory...
   ```

3. **Ask questions**:
   ```
   You: What is machine learning?
   Agent: [Agent reasoning steps shown]
   Based on the documents I found, machine learning is...
   ```

4. **Use different agent types**:
   ```bash
   # Research-focused agent
   python -m bleen_agent.cli --agent-type researcher

   # Document management agent
   python -m bleen_agent.cli --agent-type librarian

   # Analysis-focused agent
   python -m bleen_agent.cli --agent-type analyst
   ```

## Architecture

### Core Components

- **Agent Core**: LlamaIndex ReAct agent with tool orchestration and reasoning
- **Tool System**: Modular tools for search, document management, and analysis
- **Memory System**: Conversation memory with automatic summarization
- **Document Parser**: Extracts structured information from Markdown and text files
- **Solr Integration**: Full-text search and document storage without embeddings
- **CLI Interface**: Interactive command-line interface with agent reasoning visibility

### Available Tools

1. **Search Tools**:
   - `document_search`: Full-text search with relevance ranking
   - `filtered_search`: Search with metadata filters (author, category, etc.)
   - `find_similar_documents`: Content-based similarity without embeddings

2. **Management Tools**:
   - `import_documents`: Import files and directories with automatic processing
   - `delete_document`: Remove documents from the collection
   - `get_document_details`: Detailed document information and metadata

3. **Analysis Tools**:
   - `analyze_query`: Intelligent query analysis and strategy recommendation
   - `get_collection_stats`: Collection statistics and health metrics

### Agent Types

- **Full Agent**: All tools available for comprehensive capabilities
- **Researcher**: Optimized for information discovery and analysis
- **Librarian**: Focused on document management and organization
- **Analyst**: Specialized in query analysis and collection insights

## Configuration

### Environment Variables

```bash
# OpenAI Configuration
OPENAI_API_KEY=your_openai_api_key_here

# Solr Configuration
SOLR_URL=http://localhost:8983/solr
SOLR_COLLECTION=bleen_documents

# Agent Configuration
LOG_LEVEL=INFO
MAX_CHUNK_SIZE=1000
CHUNK_OVERLAP=200
MAX_SEARCH_RESULTS=10

# LLM Configuration
LLM_MODEL=gpt-3.5-turbo
LLM_TEMPERATURE=0.1
LLM_MAX_TOKENS=2000
```

### Document Schema

Documents are stored with the following structure:

- **Core Fields**: id, document_id, content, title
- **Metadata**: author, category, tags, language, document_type
- **Temporal**: created_date, modified_date, indexed_date
- **Metrics**: content_length, word_count, readability_score
- **Vectors**: content_vector (OpenAI embeddings)
- **Custom Fields**: Extracted from frontmatter

## Usage Examples

### Document Import

```bash
# Import a single file
python -m bleen_agent.import_docs import-file document.md

# Import a directory
python -m bleen_agent.import_docs import-directory ./docs --recursive

# Import without chunking
python -m bleen_agent.import_docs import-directory ./docs --no-chunk

# Check collection status
python -m bleen_agent.import_docs status
```

### CLI Commands

```bash
# Start interactive session
python -m bleen_agent.cli

# Within the CLI:
/help              # Show available commands
/stats             # Show collection statistics
/history           # Show conversation history
/search <query>    # Search documents directly
/similar <doc_id>  # Find similar documents
/clear             # Clear conversation history
/quit              # Exit
```

### Example Questions

The agent can handle various types of questions:

**Factual Questions**:
- "What is the definition of artificial intelligence?"
- "When was the company founded?"
- "Who wrote the documentation on API endpoints?"

**Conceptual Questions**:
- "How does machine learning work?"
- "Explain the concept of microservices"
- "What are the principles of good software design?"

**Procedural Questions**:
- "How do I set up the development environment?"
- "What are the steps to deploy the application?"
- "How to configure the database connection?"

**Comparative Questions**:
- "What's the difference between REST and GraphQL?"
- "Compare Python and JavaScript for web development"
- "Which approach is better for data processing?"

## Advanced Features

### Custom Metadata

Add custom metadata to your documents using frontmatter:

```markdown
---
title: "Advanced Machine Learning Techniques"
author: ["John Doe", "Jane Smith"]
category: ["AI", "Research"]
tags: ["machine-learning", "deep-learning", "neural-networks"]
difficulty: "advanced"
last_updated: "2024-01-15"
---

# Your document content here...
```

### Search Filters

Use filters in your queries:

```
# Search by author
"machine learning by John Doe"

# Search by category
"category:AI what is deep learning"

# Search by document type
"type:markdown explain neural networks"
```

### Programmatic Usage

```python
from bleen_agent import BleenAgent

# Initialize agent
agent = BleenAgent()

# Ask a question
response = agent.ask("What is machine learning?")
print(response["answer"])

# Search documents
results = agent.search_documents("neural networks", search_type="hybrid")

# Get similar documents
similar = agent.get_similar_documents("doc_123")
```

## Development

### Project Structure

```
bleen-agent/
├── bleen_agent/
│   ├── core/           # Core agent logic
│   ├── models/         # Data models
│   ├── solr/           # Solr integration
│   ├── import_system/  # Document import
│   ├── cli/            # Command-line interface
│   └── utils/          # Utilities
├── solr_schema/        # Solr configuration
├── tests/              # Test suite
├── docs/               # Documentation
└── examples/           # Example documents
```

### Running Tests

```bash
# Install development dependencies
pip install -e ".[dev]"

# Run tests
pytest

# Run with coverage
pytest --cov=bleen_agent
```

### Code Quality

```bash
# Format code
black bleen_agent/
isort bleen_agent/

# Type checking
mypy bleen_agent/
```

## Troubleshooting

### Common Issues

1. **Solr Connection Failed**:
   - Ensure Solr is running: `bin/solr status`
   - Check the SOLR_URL in your .env file
   - Verify the collection exists: `bin/solr list`

2. **OpenAI API Errors**:
   - Verify your API key is correct
   - Check your OpenAI account has sufficient credits
   - Ensure you have access to the specified model

3. **Import Failures**:
   - Check file permissions and paths
   - Verify document format (UTF-8 encoding)
   - Look at the import logs for specific errors

4. **Poor Search Results**:
   - Try different search strategies (/search command)
   - Check if documents were indexed properly (/stats command)
   - Consider adjusting chunk size for your document types

### Performance Tuning

- **Chunk Size**: Adjust MAX_CHUNK_SIZE based on your document types
- **Search Results**: Tune MAX_SEARCH_RESULTS for response time vs. quality
- **Solr Memory**: Increase Solr heap size for large collections
- **Embedding Cache**: Consider caching embeddings for frequently accessed documents

## Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Add tests
5. Submit a pull request

## License

MIT License - see LICENSE file for details.

## Support

- Create an issue for bugs or feature requests
- Check the documentation in the `docs/` directory
- Review example configurations in `examples/`
