# Bleen Agent Configuration
# Copy this file to .env and configure your settings

# =============================================================================
# OpenAI Configuration (REQUIRED)
# =============================================================================
# Get your API key from: https://platform.openai.com/api-keys
OPENAI_API_KEY=your_openai_api_key_here

# =============================================================================
# Solr Configuration
# =============================================================================
# Solr server URL (use solr:8983 for Docker, localhost:8983 for local)
SOLR_URL=http://localhost:8983/solr
SOLR_COLLECTION=bleen_documents

# =============================================================================
# Agent Configuration
# =============================================================================
# Logging level: DEBUG, INFO, WARNING, ERROR
LOG_LEVEL=INFO

# Maximum search results to return
MAX_SEARCH_RESULTS=10

# Agent behavior
AGENT_MAX_ITERATIONS=10
AGENT_VERBOSE=true

# =============================================================================
# LLM Configuration
# =============================================================================
# OpenAI model for the main agent
LLM_MODEL=gpt-4-turbo-preview

# Temperature for response generation (0.0 = deterministic, 1.0 = creative)
LLM_TEMPERATURE=0.1

# Maximum tokens for LLM responses
LLM_MAX_TOKENS=4000

# =============================================================================
# Memory Configuration
# =============================================================================
# Maximum tokens to keep in conversation memory
MEMORY_MAX_TOKENS=8000

# Model for memory summarization (can be cheaper than main model)
MEMORY_SUMMARY_MODEL=gpt-3.5-turbo

# =============================================================================
# Tool Configuration
# =============================================================================
# Timeout for tool execution (seconds)
TOOL_TIMEOUT=30

# Enable caching for tool results
ENABLE_TOOL_CACHING=true

# =============================================================================
# Document Processing Configuration
# =============================================================================
# Maximum chunk size for document splitting
MAX_CHUNK_SIZE=2000

# Overlap between chunks
CHUNK_OVERLAP=200

# Supported file types for import (comma-separated)
SUPPORTED_FILE_TYPES=.md,.txt,.pdf,.docx

# =============================================================================
# Docker-specific Configuration
# =============================================================================
# For Docker Compose, Solr URL should be:
# SOLR_URL=http://solr:8983/solr

# For development mode:
# LOG_LEVEL=DEBUG
# AGENT_VERBOSE=true
