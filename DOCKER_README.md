# 🐳 Bleen Agent Docker Setup

Quick start guide for running Bleen Agent with Docker.

## 🚀 Quick Start

```bash
# 1. Clone and setup
git clone <repository-url>
cd bleen-agent
make quickstart

# 2. Add your OpenAI API key to .env
nano .env

# 3. Start services
make start
```

## 📋 Prerequisites

- Docker 20.10+
- Docker Compose 2.0+
- OpenAI API key
- 4GB+ RAM

## 🛠️ Available Commands

### Service Management
```bash
make start          # Start production services
make start-dev      # Start with Jupy<PERSON> notebook
make start-bg       # Start in background
make stop           # Stop all services
make clean          # Stop and remove data
```

### Development
```bash
make shell          # Open agent container shell
make logs           # View service logs
make status         # Check service status
make health         # Health check
```

### Agent Types
```bash
make agent-researcher   # Research-focused agent
make agent-librarian    # Document management agent
make agent-analyst      # Analysis-focused agent
```

## 🌐 Access Points

- **Agent CLI**: Runs automatically in container
- **Solr Admin**: http://localhost:8983
- **Jupyter** (dev mode): http://localhost:8889

## 📁 Project Structure

```
bleen-agent/
├── devops/                    # DevOps configurations
│   ├── docker/                # Docker configurations
│   │   ├── docker-compose.yml         # Production setup
│   │   ├── docker-compose.dev.yml     # Development setup
│   │   ├── docker-compose.solr-only.yml  # Solr-only setup
│   │   ├── Dockerfile                 # Production image
│   │   └── Dockerfile.dev             # Development image
│   ├── scripts/               # Automation scripts
│   │   ├── start.sh               # Start script
│   │   ├── stop.sh                # Stop script
│   │   ├── start-solr.sh          # Start Solr only
│   │   └── stop-solr.sh           # Stop Solr only
│   └── configs/               # Configuration files
├── Makefile                   # Convenience commands
└── .env.example               # Configuration template
```

## ⚙️ Configuration

### Required Environment Variables

```bash
# .env file
OPENAI_API_KEY=your_api_key_here
SOLR_URL=http://solr:8983/solr
SOLR_COLLECTION=bleen_documents
```

### Optional Settings

```bash
LLM_MODEL=gpt-4-turbo-preview
LOG_LEVEL=INFO
AGENT_VERBOSE=true
MEMORY_MAX_TOKENS=8000
```

## 🔧 Troubleshooting

### Common Issues

1. **Solr connection failed**
   ```bash
   curl http://localhost:8983/solr/admin/ping
   make logs
   ```

2. **Agent exits immediately**
   ```bash
   # Check API key in .env
   docker logs bleen-agent-app
   ```

3. **Port conflicts**
   ```bash
   # Check what's using port 8983
   netstat -tulpn | grep :8983
   ```

### Reset Everything

```bash
make clean          # Removes all data
make rebuild        # Rebuilds images
make start          # Fresh start
```

## 📊 Monitoring

```bash
# Resource usage
docker stats

# Service health
make health

# Logs
make logs
```

## 🔄 Development Workflow

```bash
# Start development environment
make start-dev

# Open Jupyter notebook
open http://localhost:8889

# Make code changes (auto-reloaded)
# Test in container
make shell

# View logs
make logs
```

## 📚 Documentation

- [Full Docker Guide](docs/DOCKER.md)
- [Architecture Overview](ARCHITECTURE.md)
- [Setup Guide](docs/SETUP.md)

## 🆘 Getting Help

1. Check logs: `make logs`
2. Verify health: `make health`
3. Reset if needed: `make clean && make start`
4. See full documentation in `docs/`

---

**Need more details?** See the complete [Docker documentation](docs/DOCKER.md) for advanced configuration, troubleshooting, and production deployment.
