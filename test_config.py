#!/usr/bin/env python3
"""
Test script to identify configuration issues
"""

def test_config():
    print("Testing Bleen Agent configuration...")
    
    try:
        print("1. Testing basic imports...")
        import os
        import sys
        print("✅ Basic imports OK")
        
        print("2. Testing pydantic...")
        from pydantic import Field
        from pydantic_settings import BaseSettings
        print("✅ Pydantic imports OK")
        
        print("3. Testing config import...")
        from bleen_agent.core.config import Settings
        print("✅ Config class import OK")
        
        print("4. Testing config instantiation...")
        settings = Settings()
        print("✅ Config instantiation OK")
        
        print("5. Testing config values...")
        print(f"  - SOLR_URL: {settings.solr_url}")
        print(f"  - SOLR_COLLECTION: {settings.solr_collection}")
        print(f"  - OPENAI_API_KEY: {'***' if settings.openai_api_key else 'NOT SET'}")
        print(f"  - Supported file types: {settings.supported_file_types}")
        print("✅ Config values OK")
        
        print("6. Testing full agent import...")
        from bleen_agent import create_agent
        print("✅ Agent import OK")
        
        print("\n🎉 All tests passed! The agent should work now.")
        return True
        
    except Exception as e:
        print(f"\n❌ Error at step: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    success = test_config()
    if success:
        print("\nYou can now run: python -m bleen_agent.cli")
    else:
        print("\nPlease fix the errors above before running the agent.")
