# 🚀 Bleen Agent Setup Guide

Simple guide to get Bleen Agent running quickly.

## Prerequisites

- **Docker**: For running Solr
- **Python 3.9+**: For the agent
- **OpenAI API Key**: Required for AI functionality

## Quick Setup

### Option 1: Automated Setup
```bash
git clone <repository-url>
cd bleen-agent
./setup.sh
```

### Option 2: Manual Setup
```bash
# 1. Clone repository
git clone <repository-url>
cd bleen-agent

# 2. Setup environment
cp .env.example .env
nano .env  # Add your OpenAI API key

# 3. Install dependencies
pip install -r requirements.txt
pip install -e .

# 4. Start Solr
make solr-start

# 5. Start agent
python -m bleen_agent.cli
```

## Configuration

### Environment Variables (.env)
```bash
# Required
OPENAI_API_KEY=your_openai_api_key_here

# Solr (default values)
SOLR_URL=http://localhost:8983/solr
SOLR_COLLECTION=bleen_documents

# Optional
LOG_LEVEL=INFO
AGENT_VERBOSE=true
```

## Usage

### Starting Services
```bash
# Start Solr
make solr-start

# Check Solr status
make status

# View Solr logs
make solr-logs
```

### Using the Agent
```bash
# Start the agent
python -m bleen_agent.cli

# Different agent types
python -m bleen_agent.cli --agent-type researcher
python -m bleen_agent.cli --agent-type librarian
python -m bleen_agent.cli --agent-type analyst
```

### CLI Commands
Within the agent CLI:
```
/help           # Show available commands
/import ./docs  # Import documents
/stats          # Collection statistics
/tools          # Available tools
/quit           # Exit
```

## Solr Management

### Basic Commands
```bash
make solr-start    # Start Solr container
make solr-stop     # Stop Solr container
make solr-logs     # View logs
make solr-clean    # Stop and remove data
make solr-restart  # Restart Solr
make solr-shell    # Open shell in container
```

### Solr Admin UI
- **URL**: http://localhost:8983
- **Collection**: bleen_documents
- **Core Admin**: http://localhost:8983/solr/#/~cores

## Troubleshooting

### Common Issues

#### 1. Solr Won't Start
```bash
# Check if port is in use
netstat -tulpn | grep :8983

# Check Docker
docker ps
docker-compose logs

# Restart Solr
make solr-restart
```

#### 2. Agent Import Errors
```bash
# Check Solr connection
curl http://localhost:8983/solr/admin/ping

# Check collection exists
curl http://localhost:8983/solr/bleen_documents/admin/ping
```

#### 3. Dependency Conflicts
```bash
# Use flexible requirements
pip install -r requirements-flexible.txt
pip install -e .

# Or minimal requirements
pip install -r requirements-minimal.txt
pip install -e .
```

#### 4. OpenAI API Errors
- Check API key in `.env`
- Verify API key has credits
- Check network connectivity

### Reset Everything
```bash
# Stop and clean Solr
make solr-clean

# Reinstall dependencies
pip uninstall bleen-agent
pip install -e .

# Restart setup
make quickstart
```

## Development

### Code Formatting
```bash
make format    # Format code with black/isort
make lint      # Check code style
```

### Testing
```bash
make test      # Run tests
```

### Project Structure
```
bleen-agent/
├── bleen_agent/        # Main package
├── solr_schema/        # Solr configuration
├── examples/           # Sample documents
├── docker-compose.yml  # Solr container
├── Makefile           # Commands
└── setup.sh           # Setup script
```

## Advanced Usage

### Custom Solr Configuration
Edit files in `solr_schema/` and restart Solr:
```bash
make solr-clean
make solr-start
```

### Environment-Specific Settings
Create different `.env` files:
```bash
cp .env .env.development
cp .env .env.production
# Edit as needed
```

### Programmatic Usage
```python
import asyncio
from bleen_agent import create_agent

async def main():
    agent = create_agent()
    response = await agent.ask("What is machine learning?")
    print(response)

asyncio.run(main())
```

## Getting Help

### Check Status
```bash
make status    # Overall status
make health    # Health check
```

### View Logs
```bash
make solr-logs    # Solr logs
# Agent logs are in terminal
```

### Available Commands
```bash
make help    # All available commands
```

---

**Need more help?** Check the main [README.md](README.md) or create an issue in the repository.
