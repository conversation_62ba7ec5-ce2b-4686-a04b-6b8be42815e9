# Docker Deployment Guide

This guide covers deploying Bleen Agent using Dock<PERSON> and Docker Compose for both development and production environments.

## Prerequisites

- Docker 20.10+
- Docker Compose 2.0+
- At least 4GB RAM available for containers
- OpenAI API key

## Quick Start

### 1. Initial Setup

```bash
# Clone repository
git clone <repository-url>
cd bleen-agent

# Quick setup with <PERSON><PERSON><PERSON>
make quickstart
```

This will:
- Copy `.env.example` to `.env`
- Create necessary directories
- Start all services
- Display next steps

### 2. Configure Environment

Edit the `.env` file with your settings:

```bash
# Required: Add your OpenAI API key
OPENAI_API_KEY=your_actual_api_key_here

# Optional: Adjust other settings
LLM_MODEL=gpt-4-turbo-preview
LOG_LEVEL=INFO
```

### 3. Start Services

```bash
# Production mode
make start

# Development mode (includes Jupyter)
make start-dev

# Background mode
make start-bg
```

## Architecture Overview

### Services

#### Solr Service
- **Image**: `solr:9.4`
- **Port**: `8983`
- **Purpose**: Document storage and full-text search
- **Volume**: `solr_data` for persistence
- **Health Check**: Automatic ping monitoring

#### Bleen Agent Service
- **Build**: Custom image from `Dockerfile`
- **Purpose**: Main application with CLI interface
- **Depends On**: Solr (with health check)
- **Volumes**: Examples and docs mounted for easy access

#### Solr Admin (Optional)
- **Image**: `nginx:alpine`
- **Port**: `8080`
- **Purpose**: Proxy to Solr admin interface

### Networks

All services run on the `bleen-network` bridge network for internal communication.

### Volumes

- `solr_data`: Persistent Solr data
- `agent_data`: Application data and logs

## Configuration

### Environment Variables

The Docker setup uses environment variables for configuration:

```bash
# Core Configuration
OPENAI_API_KEY=your_key_here
SOLR_URL=http://solr:8983/solr  # Internal Docker network
SOLR_COLLECTION=bleen_documents

# Agent Configuration
LLM_MODEL=gpt-4-turbo-preview
AGENT_VERBOSE=true
LOG_LEVEL=INFO

# Memory and Performance
MEMORY_MAX_TOKENS=8000
MAX_SEARCH_RESULTS=10
```

### Docker-Specific Settings

When running in Docker, certain settings are automatically configured:

- Solr URL uses internal Docker networking (`solr:8983`)
- Volumes are mounted for data persistence
- Health checks ensure proper startup order

## Development Mode

### Features

Development mode includes additional features:

```bash
make start-dev
```

**Includes:**
- Jupyter notebook server (port 8889)
- Code hot-reloading
- Development dependencies
- Debug logging
- Source code mounted as volume

### Development Services

```yaml
# Additional services in development
jupyter:
  ports:
    - "8889:8888"
  command: jupyter notebook --allow-root
```

### Accessing Development Tools

```bash
# Jupyter notebook
open http://localhost:8889

# Shell access
make shell

# View logs
make logs
```

## Production Deployment

### Optimizations

Production mode includes:

- Multi-stage Docker build for smaller images
- Non-root user for security
- Health checks for monitoring
- Optimized memory settings

### Security Considerations

```dockerfile
# Non-root user
RUN groupadd -r appuser && useradd -r -g appuser appuser
USER appuser

# Health checks
HEALTHCHECK --interval=30s --timeout=10s CMD python -c "..."
```

### Resource Limits

Add resource limits in production:

```yaml
services:
  bleen-agent:
    deploy:
      resources:
        limits:
          memory: 2G
          cpus: '1.0'
        reservations:
          memory: 1G
          cpus: '0.5'
```

## Management Commands

### Using Makefile

```bash
# Service management
make start          # Start production services
make start-dev      # Start development services
make stop           # Stop all services
make clean          # Stop and remove volumes
make rebuild        # Rebuild images from scratch

# Development
make shell          # Open shell in agent container
make solr-shell     # Open shell in Solr container
make logs           # View service logs
make status         # Show service status

# Agent variants
make agent-researcher   # Start researcher agent
make agent-librarian    # Start librarian agent
make agent-analyst      # Start analyst agent

# Utilities
make health         # Check service health
make import-samples # Import example documents
```

### Using Scripts

```bash
# Start script with options
./scripts/start.sh --mode development --agent-type researcher --detached

# Stop script with cleanup
./scripts/stop.sh --remove-volumes
```

### Direct Docker Compose

```bash
# Production
docker-compose up -d

# Development
docker-compose -f docker-compose.dev.yml up -d

# Stop and cleanup
docker-compose down --volumes
```

## Troubleshooting

### Common Issues

#### 1. Solr Connection Failed

```bash
# Check Solr health
curl http://localhost:8983/solr/admin/ping

# Check container logs
docker logs bleen-solr

# Restart Solr
docker-compose restart solr
```

#### 2. Agent Container Exits

```bash
# Check agent logs
docker logs bleen-agent-app

# Common causes:
# - Missing OpenAI API key
# - Solr not ready
# - Configuration errors
```

#### 3. Permission Issues

```bash
# Fix volume permissions
sudo chown -R $USER:$USER ./data ./logs

# Or use Docker user mapping
docker-compose run --user $(id -u):$(id -g) bleen-agent bash
```

#### 4. Port Conflicts

```bash
# Check port usage
netstat -tulpn | grep :8983

# Change ports in docker-compose.yml
ports:
  - "9983:8983"  # Use different external port
```

### Health Checks

```bash
# Service health
make health

# Manual checks
curl http://localhost:8983/solr/admin/ping
docker ps --filter "name=bleen"
```

### Logs and Debugging

```bash
# All service logs
make logs

# Specific service logs
docker logs bleen-agent-app
docker logs bleen-solr

# Follow logs
docker logs -f bleen-agent-app

# Debug mode
LOG_LEVEL=DEBUG make start-dev
```

## Data Management

### Backup

```bash
# Backup Solr data
docker run --rm -v bleen-agent_solr_data:/data -v $(pwd):/backup alpine tar czf /backup/solr-backup.tar.gz /data

# Backup application data
docker run --rm -v bleen-agent_agent_data:/data -v $(pwd):/backup alpine tar czf /backup/agent-backup.tar.gz /data
```

### Restore

```bash
# Restore Solr data
docker run --rm -v bleen-agent_solr_data:/data -v $(pwd):/backup alpine tar xzf /backup/solr-backup.tar.gz -C /
```

### Reset

```bash
# Complete reset (removes all data)
make clean

# Reset specific service
docker-compose stop solr
docker volume rm bleen-agent_solr_data
docker-compose up solr
```

## Performance Tuning

### Solr Optimization

```yaml
solr:
  environment:
    - SOLR_HEAP=2g                    # Increase heap size
    - SOLR_JAVA_MEM=-Xms1g -Xmx2g    # Set JVM memory
```

### Agent Optimization

```yaml
bleen-agent:
  environment:
    - MEMORY_MAX_TOKENS=16000         # Increase memory
    - MAX_SEARCH_RESULTS=20           # More results
    - TOOL_TIMEOUT=60                 # Longer timeouts
```

### Resource Monitoring

```bash
# Container resource usage
docker stats

# Specific container
docker stats bleen-agent-app

# System resources
docker system df
```

## Scaling

### Horizontal Scaling

For production deployments, consider:

```yaml
# Multiple agent instances
bleen-agent:
  deploy:
    replicas: 3
    
# Load balancer
nginx:
  image: nginx
  ports:
    - "80:80"
```

### Solr Clustering

For large deployments:

```yaml
# SolrCloud setup
zookeeper:
  image: zookeeper:3.8

solr1:
  image: solr:9.4
  environment:
    - ZK_HOST=zookeeper:2181

solr2:
  image: solr:9.4
  environment:
    - ZK_HOST=zookeeper:2181
```

This Docker setup provides a robust, scalable foundation for deploying Bleen Agent in any environment.
