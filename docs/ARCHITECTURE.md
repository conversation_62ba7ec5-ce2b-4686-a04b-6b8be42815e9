# Bleen Agent Architecture

## Overview

Bleen Agent has been completely redesigned following best practices from Anthropic's "Building Effective Agents" and OpenAI's "A Practical Guide to Building Agents". The new architecture is built on LlamaIndex and uses a sophisticated tool-based approach without embeddings for better transparency and explainability.

## Key Design Principles

### 1. Tool-Based Architecture
- **Modular Design**: Each capability is implemented as a separate tool
- **Clear Interfaces**: Tools have well-defined inputs, outputs, and purposes
- **Composability**: Tools can be combined for complex operations
- **Testability**: Each tool can be tested independently

### 2. Transparent Reasoning
- **Step-by-Step Thinking**: Agent shows its reasoning process
- **Tool Selection Logic**: Clear explanation of why specific tools are chosen
- **Error Handling**: Graceful failure with informative error messages
- **Confidence Scoring**: Quantified confidence in responses

### 3. Memory Management
- **Conversation Context**: Maintains context across interactions
- **Automatic Summarization**: Compresses old conversations when memory is full
- **Relevant Context Retrieval**: Finds relevant past interactions for current queries
- **Token-Aware**: Manages memory within token limits

### 4. No Embeddings Approach
- **Full-Text Search**: Uses Solr's powerful text analysis capabilities
- **Transparent Matching**: Users can understand why documents were retrieved
- **Keyword Highlighting**: Shows exactly what matched in search results
- **Explainable Results**: Clear relevance scoring and ranking

## Architecture Components

### Core Agent (`bleen_agent/agent/core.py`)
```
BleenAgent
├── LlamaIndex ReAct Agent
├── Tool Orchestration
├── Memory Management
├── Error Handling
└── Reasoning Transparency
```

**Key Features:**
- Built on LlamaIndex's ReAct (Reasoning + Acting) framework
- Asynchronous operation for better performance
- Comprehensive logging and debugging
- Session management and state tracking

### Tool System (`bleen_agent/tools/`)

#### Search Tools
1. **DocumentSearchTool**: Full-text search with boolean operators, phrase matching, and field-specific searches
2. **FilteredSearchTool**: Metadata-based filtering (author, category, tags, date ranges)
3. **SimilarDocumentsTool**: Content-based similarity using Solr's More Like This functionality

#### Management Tools
1. **DocumentImportTool**: Import files and directories with automatic metadata extraction
2. **DocumentDeletionTool**: Safe document removal with confirmation
3. **DocumentDetailsTool**: Detailed document information and chunk analysis

#### Analysis Tools
1. **QueryAnalysisTool**: Intelligent query classification and strategy recommendation
2. **CollectionStatsTool**: Comprehensive collection statistics and health metrics

### Memory System (`bleen_agent/memory/conversation.py`)
```
ConversationMemory
├── Interaction Storage
├── Token Management
├── Automatic Summarization
├── Context Retrieval
└── Usage Statistics
```

**Features:**
- Token-aware memory management
- Automatic summarization when approaching limits
- Relevant context retrieval for queries
- Conversation history preservation

### Agent Factory (`bleen_agent/agent/factory.py`)
```
AgentFactory
├── Full Agent (all tools)
├── Preset Agents (researcher, librarian, analyst)
├── Custom Agent (selected tools)
├── Health Check Integration
└── Configuration Management
```

**Preset Configurations:**
- **Researcher**: Optimized for information discovery and analysis
- **Librarian**: Focused on document management and organization
- **Analyst**: Specialized in query analysis and collection insights

## Data Flow

### Question Processing Flow
```
User Question
    ↓
Agent Reasoning (ReAct)
    ↓
Tool Selection
    ↓
Tool Execution
    ↓
Result Synthesis
    ↓
Response Generation
    ↓
Memory Storage
```

### Document Import Flow
```
File/Directory Input
    ↓
Document Parsing
    ↓
Metadata Extraction
    ↓
Chunking (if needed)
    ↓
Solr Indexing
    ↓
Validation
```

### Search Flow
```
Query Analysis
    ↓
Strategy Selection
    ↓
Solr Query Construction
    ↓
Search Execution
    ↓
Result Processing
    ↓
Relevance Scoring
```

## Solr Schema Design

### Core Fields
- `id`: Unique identifier
- `document_id`: Parent document reference
- `content`: Full document text
- `title`: Document title
- `is_chunk`: Boolean flag for chunks

### Metadata Fields
- `author`: Document authors (multi-valued)
- `category`: Categories (multi-valued)
- `tags`: Tags (multi-valued)
- `document_type`: File type (markdown, text, pdf)
- `language`: Document language
- `source_file`: Original filename
- `file_path`: Full file path

### Temporal Fields
- `created_date`: Document creation date
- `modified_date`: Last modification date
- `indexed_date`: Indexing timestamp

### Metrics Fields
- `content_length`: Character count
- `word_count`: Word count
- `readability_score`: Optional readability metric

### Dynamic Fields
- `custom_*`: Custom metadata from frontmatter
- `meta_*`: Additional metadata fields

## Configuration System

### Environment Variables
```bash
# OpenAI Configuration
OPENAI_API_KEY=your_api_key

# Solr Configuration
SOLR_URL=http://localhost:8983/solr
SOLR_COLLECTION=bleen_documents

# Agent Configuration
LLM_MODEL=gpt-4-turbo-preview
LLM_TEMPERATURE=0.1
AGENT_MAX_ITERATIONS=10
AGENT_VERBOSE=true

# Memory Configuration
MEMORY_MAX_TOKENS=8000
MEMORY_SUMMARY_MODEL=gpt-3.5-turbo

# Tool Configuration
TOOL_TIMEOUT=30
MAX_SEARCH_RESULTS=10
```

### Pydantic Settings
- Type validation and conversion
- Environment variable loading
- Default value management
- Configuration documentation

## CLI Interface

### Enhanced Features
- **Agent Type Selection**: Choose between full, preset, or custom agents
- **Real-time Progress**: Progress indicators for long operations
- **Rich Formatting**: Colored output, tables, and panels
- **Command System**: Slash commands for special operations
- **Conversation History**: View and manage conversation history
- **Tool Inspection**: Examine available tools and their capabilities

### Commands
- `/help`: Show available commands
- `/tools`: List available tools
- `/agent`: Show agent information
- `/preset <name>`: Switch agent preset
- `/import <path>`: Import documents
- `/stats`: Collection statistics
- `/history`: Conversation history
- `/clear`: Clear conversation
- `/quit`: Exit application

## Error Handling and Recovery

### Graceful Degradation
- Tool failures don't crash the agent
- Alternative strategies when primary tools fail
- Clear error messages with suggested solutions
- Automatic retry mechanisms for transient failures

### Health Checks
- Solr connectivity validation
- OpenAI API accessibility
- Tool functionality verification
- System resource monitoring

## Performance Considerations

### Optimization Strategies
- **Asynchronous Operations**: Non-blocking tool execution
- **Memory Management**: Automatic conversation summarization
- **Caching**: Tool result caching where appropriate
- **Batch Processing**: Efficient bulk operations

### Scalability Features
- **Stateless Design**: Agent instances can be created on-demand
- **Tool Modularity**: Easy to add/remove capabilities
- **Configuration Flexibility**: Adaptable to different environments
- **Resource Monitoring**: Track memory and token usage

## Security and Privacy

### Data Protection
- No persistent storage of conversation data
- Configurable memory limits
- Secure API key management
- Local document processing

### Access Control
- Tool-level permissions
- Configuration-based restrictions
- Audit logging capabilities
- Safe document deletion

## Future Extensibility

### Plugin Architecture
- Easy tool addition through inheritance
- Standard tool interface
- Configuration-driven tool loading
- Community tool development

### Integration Points
- REST API endpoints (future)
- Webhook support (future)
- External tool integration
- Custom LLM providers

## Comparison with Previous Architecture

### Old Architecture (v0.1.0)
- Embedding-based search
- Monolithic design
- Limited reasoning transparency
- Basic memory management
- Single search strategy

### New Architecture (v0.2.0)
- Full-text search without embeddings
- Tool-based modular design
- Complete reasoning transparency
- Advanced memory management
- Multiple search strategies
- Agent presets and customization
- Enhanced error handling
- Better performance and scalability

## Getting Started

1. **Install Dependencies**: `pip install -r requirements.txt`
2. **Configure Environment**: Copy `.env.example` to `.env` and configure
3. **Setup Solr**: Run `./solr_schema/setup_collection.sh`
4. **Start Agent**: `python -m bleen_agent.cli`
5. **Import Documents**: Use `/import` command or agent questions
6. **Ask Questions**: Natural language queries with full reasoning visibility

This architecture provides a solid foundation for intelligent document retrieval and question answering while maintaining transparency, extensibility, and performance.
