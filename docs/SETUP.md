# Bleen Agent Setup Guide

This guide provides detailed instructions for setting up Bleen Agent in various environments.

## System Requirements

### Minimum Requirements
- Python 3.9 or higher
- 4GB RAM
- 2GB disk space
- Apache Solr 9.0+
- OpenAI API access

### Recommended Requirements
- Python 3.11+
- 8GB RAM
- 10GB disk space (for document storage)
- Apache Solr 9.4+
- Dedicated server for Solr

## Installation Methods

### Method 1: Standard Installation

1. **Clone the repository**:
   ```bash
   git clone <repository-url>
   cd bleen-agent
   ```

2. **Create virtual environment**:
   ```bash
   python -m venv venv
   source venv/bin/activate  # On Windows: venv\Scripts\activate
   ```

3. **Install dependencies**:
   ```bash
   pip install -r requirements.txt
   ```

4. **Install in development mode** (optional):
   ```bash
   pip install -e .
   ```

### Method 2: Docker Installation

1. **Using Docker Compose** (includes Solr):
   ```bash
   docker-compose up -d
   ```

2. **Using individual containers**:
   ```bash
   # Start Solr
   docker run -d -p 8983:8983 --name solr solr:9.4
   
   # Build and run Bleen Agent
   docker build -t bleen-agent .
   docker run -it --link solr bleen-agent
   ```

## Apache Solr Setup

### Option 1: Local Installation

1. **Download Solr**:
   ```bash
   wget https://archive.apache.org/dist/solr/solr/9.4.1/solr-9.4.1.tgz
   tar xzf solr-9.4.1.tgz
   cd solr-9.4.1
   ```

2. **Start Solr**:
   ```bash
   bin/solr start
   ```

3. **Create collection**:
   ```bash
   cd /path/to/bleen-agent/solr_schema
   ./setup_collection.sh
   ```

### Option 2: Docker Solr

1. **Start Solr container**:
   ```bash
   docker run -d -p 8983:8983 --name solr solr:9.4
   ```

2. **Create collection**:
   ```bash
   docker exec -it solr solr create_collection -c bleen_documents
   ```

3. **Upload schema**:
   ```bash
   # Copy schema files to container
   docker cp solr_schema/schema.xml solr:/opt/solr/server/solr/bleen_documents/conf/
   docker cp solr_schema/solrconfig.xml solr:/opt/solr/server/solr/bleen_documents/conf/
   
   # Reload collection
   docker exec -it solr curl "http://localhost:8983/solr/admin/collections?action=RELOAD&name=bleen_documents"
   ```

### Option 3: Cloud Solr (SolrCloud)

For production deployments:

1. **Set up ZooKeeper ensemble**
2. **Configure Solr nodes**
3. **Upload configuration to ZooKeeper**
4. **Create collection with replication**

## Configuration

### Environment Variables

Create a `.env` file in the project root:

```bash
# Copy example configuration
cp .env.example .env
```

Edit `.env` with your settings:

```bash
# OpenAI Configuration
OPENAI_API_KEY=sk-your-actual-api-key-here

# Solr Configuration
SOLR_URL=http://localhost:8983/solr
SOLR_COLLECTION=bleen_documents

# Agent Configuration
LOG_LEVEL=INFO
MAX_CHUNK_SIZE=1000
CHUNK_OVERLAP=200
MAX_SEARCH_RESULTS=10

# LLM Configuration
LLM_MODEL=gpt-3.5-turbo
LLM_TEMPERATURE=0.1
LLM_MAX_TOKENS=2000

# Embedding Configuration
EMBEDDING_MODEL=text-embedding-ada-002
EMBEDDING_DIMENSION=1536
```

### Advanced Configuration

For production environments, consider:

1. **Solr Performance Tuning**:
   ```xml
   <!-- In solrconfig.xml -->
   <requestHandler name="/select" class="solr.SearchHandler">
     <lst name="defaults">
       <str name="echoParams">explicit</str>
       <int name="rows">10</int>
       <str name="df">content</str>
       <bool name="facet">true</bool>
       <str name="facet.field">document_type</str>
       <str name="facet.field">category</str>
     </lst>
   </requestHandler>
   ```

2. **Memory Settings**:
   ```bash
   # For Solr
   export SOLR_HEAP=4g
   
   # For Python
   export PYTHONHASHSEED=0
   ```

3. **Logging Configuration**:
   ```python
   # In your application
   import logging
   logging.basicConfig(
       level=logging.INFO,
       format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
       handlers=[
           logging.FileHandler('bleen_agent.log'),
           logging.StreamHandler()
       ]
   )
   ```

## Verification

### Test Solr Connection

```bash
# Test basic connectivity
curl "http://localhost:8983/solr/admin/ping"

# Test collection
curl "http://localhost:8983/solr/bleen_documents/admin/ping"

# Check collection status
python -m bleen_agent.import_docs test-connection
```

### Test OpenAI Connection

```python
from openai import OpenAI
client = OpenAI(api_key="your-api-key")

# Test completion
response = client.chat.completions.create(
    model="gpt-3.5-turbo",
    messages=[{"role": "user", "content": "Hello!"}]
)
print(response.choices[0].message.content)

# Test embeddings
embedding = client.embeddings.create(
    model="text-embedding-ada-002",
    input="Test text"
)
print(f"Embedding dimension: {len(embedding.data[0].embedding)}")
```

### Test Full Pipeline

```bash
# 1. Import sample documents
mkdir -p examples/docs
echo "# Test Document\nThis is a test document for Bleen Agent." > examples/docs/test.md

# 2. Import into system
python -m bleen_agent.import_docs import-directory examples/docs

# 3. Test query
python -m bleen_agent.cli
# Then ask: "What is this test document about?"
```

## Troubleshooting

### Common Setup Issues

1. **Python Version Conflicts**:
   ```bash
   # Check Python version
   python --version
   
   # Use specific Python version
   python3.11 -m venv venv
   ```

2. **Dependency Conflicts**:
   ```bash
   # Clear pip cache
   pip cache purge
   
   # Install with no cache
   pip install --no-cache-dir -r requirements.txt
   ```

3. **Solr Connection Issues**:
   ```bash
   # Check if Solr is running
   ps aux | grep solr
   
   # Check Solr logs
   tail -f solr-9.4.1/server/logs/solr.log
   
   # Test network connectivity
   telnet localhost 8983
   ```

4. **OpenAI API Issues**:
   ```bash
   # Test API key
   curl https://api.openai.com/v1/models \
     -H "Authorization: Bearer $OPENAI_API_KEY"
   ```

5. **Memory Issues**:
   ```bash
   # Monitor memory usage
   htop
   
   # Increase swap space (Linux)
   sudo fallocate -l 2G /swapfile
   sudo chmod 600 /swapfile
   sudo mkswap /swapfile
   sudo swapon /swapfile
   ```

### Performance Optimization

1. **Solr Optimization**:
   ```bash
   # Optimize Solr index
   curl "http://localhost:8983/solr/bleen_documents/update?optimize=true"
   
   # Adjust JVM settings
   export SOLR_JAVA_MEM="-Xms2g -Xmx4g"
   ```

2. **Python Optimization**:
   ```bash
   # Use faster JSON library
   pip install orjson
   
   # Enable Python optimizations
   export PYTHONOPTIMIZE=1
   ```

3. **Embedding Caching**:
   ```python
   # Consider using Redis for embedding cache
   pip install redis
   ```

## Production Deployment

### Security Considerations

1. **API Key Management**:
   - Use environment variables or secret management systems
   - Rotate API keys regularly
   - Monitor API usage

2. **Solr Security**:
   - Enable authentication
   - Use HTTPS
   - Configure firewall rules

3. **Network Security**:
   - Use VPN or private networks
   - Enable SSL/TLS
   - Regular security updates

### Monitoring

1. **Application Monitoring**:
   ```python
   # Add health check endpoint
   from flask import Flask
   app = Flask(__name__)
   
   @app.route('/health')
   def health_check():
       return {"status": "healthy", "timestamp": datetime.utcnow()}
   ```

2. **Solr Monitoring**:
   - Monitor query performance
   - Track index size and growth
   - Set up alerts for errors

3. **Resource Monitoring**:
   - CPU and memory usage
   - Disk space
   - Network bandwidth

### Backup and Recovery

1. **Solr Backup**:
   ```bash
   # Create backup
   curl "http://localhost:8983/solr/bleen_documents/replication?command=backup&location=/backup/path"
   
   # Restore backup
   curl "http://localhost:8983/solr/bleen_documents/replication?command=restore&location=/backup/path"
   ```

2. **Configuration Backup**:
   - Version control all configuration files
   - Regular backups of .env files
   - Document deployment procedures

This completes the setup guide. For additional help, refer to the main README.md or create an issue in the repository.
