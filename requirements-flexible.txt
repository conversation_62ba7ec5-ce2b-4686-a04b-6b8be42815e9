# Flexible requirements - let pip resolve conflicts automatically
# Use this if you encounter dependency conflicts

# Core LlamaIndex dependencies (flexible versions)
llama-index
llama-index-agent-openai
llama-index-llms-openai
llama-index-tools-requests
openai

# Document processing and search
pysolr
python-frontmatter
tiktoken

# CLI and utilities
click
rich
python-dotenv

# Configuration
pydantic
pydantic-settings

# Basic utilities
requests
